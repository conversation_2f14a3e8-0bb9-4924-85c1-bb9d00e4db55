importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

firebase.initializeApp({
  apiKey: "AIzaSyA2mzyIz-ZWf68CREXm5paDYknKeizwbBU",
  authDomain: "smartlearn-lms.firebaseapp.com",
  projectId: "smartlearn-lms",
  storageBucket: "smartlearn-lms.appspot.com",
  messagingSenderId: "464527218978",
  appId: "1:464527218978:web:75598ba1fbc08d4f756d7b",
  measurementId: "G-CWZ8DQVR35",
});

const messaging = firebase.messaging();
messaging.onBackgroundMessage(function (payload) {
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});
