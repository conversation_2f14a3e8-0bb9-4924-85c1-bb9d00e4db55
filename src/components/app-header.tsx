"use client";
import React, { useEffect, useState } from "react";
import {
  BarChartIcon,
  Bell,
  BookOpenText,
  CalendarPlus,
  ChevronDown,
  HistoryIcon,
  HomeIcon,
  LogOut,
  Search,
  Settings,
  User,
  Video,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { UseTopics } from "@/hooks/useTopics";
import { getLocalStorageItem } from "@/lib/utils";
import {
  CourseData,
  CustomBrandingDetails,
  ErrorCatch,
  ToastType,
  TopicsData,
} from "@/types";
import { KEYS } from "@/lib/keys";
import { useToast } from "@/components/ui/use-toast";
import { Modal } from "@/components/ui/modal";
import ComfirmSubmit from "@/components/ui/confirmModal";
import { usePathname, useRouter } from "next/navigation";
import NotificationMenu from "./ui/user-notifications";
import Breadcrumb from "./breadcrumb";
import Image from "next/image";
import { useTheme } from "@/context/ThemeContext";
import { useTranslation } from "next-i18next";

interface AppHeaderProps {
  notifications?: number; // Optional prop with a default value of 0
  title: string; // Required prop
  onCourseChange: (course: CourseData) => void;
}

const AppHeader = ({
  notifications = 0,
  title,
  onCourseChange,
}: AppHeaderProps) => {
  const { t, i18n } = useTranslation("common");
  const { getTopicsData } = UseTopics();
  const { toast } = useToast() as ToastType;
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [courseData, setCoursData] = useState<CourseData[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<CourseData | null>(null);
  const [openModal, setIsOpenModal] = useState<boolean>(false);
  const [orgID, setOrgID] = useState<string | null>(null);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [imageUrl, setImageUrl] = useState<string>("");
  const router = useRouter();
  const pathname = usePathname();
  const { theme, setTheme } = useTheme();
  const ORG_NAME_KEY = getLocalStorageItem(KEYS.ORG_NAME);
  const [customBranding, setCustomBranding] = useState<CustomBrandingDetails>();

  useEffect(() => {
    if (pathname === "/pages/dashboard") {
      setShowDropdown(true);
    } else {
      setShowDropdown(false);
    }
    let data = getLocalStorageItem("courseData");
    if (data) {
      let parsedData = JSON.parse(data as string);
      setCoursData(parsedData);
      setSelectedCourse(parsedData[0]);
    }

    if (typeof window !== "undefined") {
      setOrgID(getLocalStorageItem(KEYS.ORG_ID));
    }
    const profileImage = localStorage.getItem("profile_image");
    if (profileImage !== null) {
      const parsedData = JSON.parse(profileImage) as {
        avatar_url?: string;
      }[];
      const image_url = parsedData[0]?.avatar_url;
      if (image_url != null) {
        setImageUrl(image_url);
      } else {
        setImageUrl("/images/profile.png");
      }
    } else {
      setImageUrl("/images/profile.png");
    }

    const brandingDetails = localStorage.getItem("brandingDetails");
    if (brandingDetails) {
      try {
        const theme_name = localStorage.getItem("theme");
        const parsedBranding: CustomBrandingDetails[] =
          JSON.parse(brandingDetails);
        const brand = parsedBranding?.find((b) => b.theme_name === theme_name);
        setCustomBranding(brand);
      } catch (error) {
        console.error(
          "Failed to parse branding details from localStorage",
          error
        );
      }
    }
  }, []);

  useEffect(() => {
    if (orgID) {
      // fetchTopics();
    }
  }, [orgID]);

  const closeModal = (): void => {
    setIsOpenModal(!openModal);
  };
  const goToProfile = (): void => {
    if (pathname !== "/pages/exam-view") {
      router.push("/pages/profile");
    }
  };

  const handleCourseSelect = (course: CourseData) => {
    setSelectedCourse(course);
    onCourseChange(course);
    router.replace("/pages/dashboard");
    if (typeof window !== "undefined") {
      localStorage.setItem(KEYS.COURSE_ID, course.course_id);
    }
  };
  useEffect(() => {
    if (!openModal) {
      document.body.style.removeProperty("pointer-events");
      document.body.style.removeProperty("overflow");
    }
    return () => {
      document.body.style.removeProperty("pointer-events");
      document.body.style.removeProperty("overflow");
    };
  }, [openModal]);

  return (
    <div>
      <header className="h-16 bg-[var(--color-primary)] backdrop-blur-xl px-4 flex items-center justify-between sticky top-0 z-50 transition-all duration-500 border-b border-white/90 font-custom">
        {/* Logo and Breadcrumb */}

        <div className="flex items-center gap-4 ml-auto ">
          {/* Logo */}

          <div className="relative w-[120] h-full">
            <Image
              src={
                customBranding
                  ? (customBranding.main_logo as string)
                  : "/images/smartlearn.png"
              }
              // src="/images/smartlearn.png"
              alt="SmartLearn Logo"
              // layout="fill"
              objectFit="contain"
              width="70"
              height="70"
            />
          </div>

          {/* Breadcrumb */}
          {/* <div className="hidden sm:block">
            <Breadcrumb
              variant="gradient"
              excludePaths={["/pages/login", "/onboarding"]}
              containerClasses="truncate text-sm"
              showTooltips={true}
            />
          </div> */}
        </div>

        {/* Title */}
        <div className="flex-grow text-center">
          <span className="text-white font-semibold text-lg sm:text-lg">
            {ORG_NAME_KEY}
            <h6 className="font-normal text-xs sm:text-sm md:text-lg lg:text-xl">
              {/* {title} */}
            </h6>
          </span>
        </div>
        <div className="flex items-center gap-4">
          <div className="relative">
            <button className="rounded-lg hover:bg-white/50 transition-all duration-300">
              {/* <Bell className="h-5 w-5 text-gray-600" /> */}
              {notifications > 0 && (
                <span className="absolute top-1 right-1 h-4 w-4 bg-pink-500 rounded-full text-xs text-white flex items-center justify-center animate-pulse">
                  {notifications}
                </span>
              )}
            </button> 
           <NotificationMenu></NotificationMenu>
          </div>
        </div>
        {/* Language Switcher Dropdown */}
        <div className="mr-4 ml-4">
          <select
            value={i18n.language}
            onChange={(e) => i18n.changeLanguage(e.target.value)}
            className="rounded px-2 py-1 text-sm border border-gray-300"
          >
            <option value="en">En</option>
            <option value="ar">Ar</option>
          </select>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger>
            <div className="relative w-10 h-10 rounded-full overflow-hidden cursor-pointer">
              <img
                src={imageUrl}
                alt="Avatar"
                className="w-10 h-10 object-cover border-2 border-white rounded-full cursor-pointer"
                onClick={goToProfile}
              />
            </div>
          </DropdownMenuTrigger>
          {pathname !== "/pages/exam-view" && (
            <DropdownMenuContent
              align="end"
              className="bg-white/95 backdrop-blur-lg cursor-pointer"
            >
              <DropdownMenuItem
                className="hover:bg-indigo-50/80 transition-all duration-300 cursor-pointer font-custom text-[var(--color-font-color)]"
                onClick={() => router.push("/pages/dashboard")}
              >
                <HomeIcon className="text-primary"></HomeIcon>
                {t("Home")}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="hover:bg-indigo-50/80 transition-all duration-300 cursor-pointer font-custom text-[var(--color-font-color)]"
                onClick={() => router.push("/pages/profile")}
              >
                <User className="text-primary" />
                {t("Profile")}
              </DropdownMenuItem>
                 <DropdownMenuItem
                className="hover:bg-indigo-50/80 transition-all duration-300 cursor-pointer font-custom text-[var(--color-font-color)]"
                onClick={() => router.push("/pages/courses")}
              >
                <BookOpenText className="text-primary" />
                 {t("Explore Courses")}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="hover:bg-indigo-50/80 transition-all duration-300 cursor-pointer font-custom text-[var(--color-font-color)]"
                onClick={() => router.push("/pages/course-rank-list")}
              >
                <BarChartIcon className="text-primary"></BarChartIcon>
                {t("Rank List")}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="hover:bg-indigo-50/80 transition-all duration-300 cursor-pointer font-custom text-[var(--color-font-color)]"
                onClick={() => router.push("/pages/exam-history")}
              >
                <HistoryIcon className="text-primary"></HistoryIcon>
                {t("Exam History")}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="hover:bg-indigo-50/80 transition-all duration-300 cursor-pointer font-custom text-[var(--color-font-color)]"
                onClick={() => router.push("/pages/subscriptions")}
              >
                <CalendarPlus className="text-primary" />
                {t("Subscription")}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="hover:bg-indigo-50/80 transition-all duration-300 cursor-pointer font-custom text-[var(--color-font-color)]"
                onClick={() => router.push("/pages/join-meeting")}
              >
                <Video className="text-primary" />
                {t("Join Meeting")}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="hover:bg-indigo-50/80 transition-all duration-300 cursor-pointer font-custom text-[var(--color-font-color)]"
                onClick={() => setIsOpenModal(true)}
              >
                <LogOut className="text-primary font-custom text-[var(--color-font-color)]" />
                {t("Sign out")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          )}
        </DropdownMenu>
        {openModal && (
          <Modal
            title={""}
            header=""
            openDialog={openModal}
            closeDialog={closeModal}
            type="max-w-m mx-auto"
          >
            <ComfirmSubmit
              onSave={closeModal}
              onCancel={closeModal}
              isModal={true}
            />
          </Modal>
        )}
      </header>
    </div>
  );
};

export default AppHeader;
