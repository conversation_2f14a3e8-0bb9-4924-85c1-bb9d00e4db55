import type { ReactNode } from "react";
import React from "react";
import Link from "next/link";
import { Home } from "lucide-react";
import { useTranslation } from "react-i18next";
interface BreadcrumbItem {
  name: string;
  path: string;
  clickable?: boolean;
}

interface TBreadCrumbProps {
  items: BreadcrumbItem[];
  separator: ReactNode;
  containerClasses?: string;
  listClasses?: string;
  capitalizeLinks?: boolean;
}

const toCamelCase = (str: string): string => {
  return str
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
};

const NextBreadcrumb: React.FC<TBreadCrumbProps> = ({
  items,
  separator,
  containerClasses,
  listClasses,
  capitalizeLinks,
}) => {
  const { t } = useTranslation("common");
  return (
    <div className={`flex ${containerClasses} pt-1 `}>
      <ul className="flex items-center space-x-2 breadcrumb-list text-sm cursor-pointer text-[var(--color-nav-text)]  ">
        {items.map((item, index) => (
          <React.Fragment key={index}>
            <li className={`text-[var(--color-nav-text)] ${listClasses}`}>
              {index === 0 ? (
                item.clickable ? (
                  <Link
                    href={item.path}
                    className={`text-[var(--color-nav-text)] hover:text-[var(--color-nav-hover-text)]  flex items-center`}
                  >
                    <Home className="mr-1" />

                    {/* {capitalizeLinks ? toCamelCase(item.name) : item.name} */}
                    {capitalizeLinks ? toCamelCase(t(item.name)) : t(item.name)}
                  </Link>
                ) : (
                  <span className="text-[var(--color-nav-text)] hover:text-[var(--color-nav-hover-text)] flex items-center">
                    <Home className="mr-1" />

                    {capitalizeLinks ? toCamelCase(t(item.name)) : t(item.name)}
                    {/* {capitalizeLinks ? toCamelCase(item.name) : item.name} */}
                  </span>
                )
              ) : index === items.length - 1 ? (
                <span className="text-[var(--color-nav-text)] hover:text-[var(--color-nav-hover-text)] font-semibold">
                  {/* {capitalizeLinks ? toCamelCase(item.name) : item.name} */}
                  {capitalizeLinks ? toCamelCase(t(item.name)) : t(item.name)}
                </span>
              ) : item.clickable === false ? (
                <span className="text-[var(--color-nav-text)] hover:text-[var(--color-nav-hover-text)]">
                  {/* {capitalizeLinks ? toCamelCase(item.name) : item.name} */}
                  {capitalizeLinks ? toCamelCase(t(item.name)) : t(item.name)}
                </span>
              ) : (
                <Link
                  href={item.path}
                  className="text-[var(--color-nav-text)] hover:text-[var(--color-nav-hover-text)] transition-colors"
                >
                  {/* {capitalizeLinks ? toCamelCase(item.name) : item.name} */}
                  {capitalizeLinks ? toCamelCase(t(item.name)) : t(item.name)}
                </Link>
              )}
            </li>
            {index !== items.length - 1 && (
              <span className="mx-1">{separator}</span>
            )}
          </React.Fragment>
        ))}
      </ul>
    </div>
  );
};

export default NextBreadcrumb;
