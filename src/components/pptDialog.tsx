"use client";

import { But<PERSON> } from "@/components/ui/button";
import { KEYS } from "@/lib/keys";
import { getLocalStorageItem } from "@/lib/utils";
import { useEffect, useState } from "react";
import { useTranslation } from "next-i18next";
export default function DocumentDialog({
  url,
  onCancel,
  pageCount = 0,
  showTopic,
}: {
  onCancel: () => void;
  url?: string;
  pageCount?: number;
  showTopic?: boolean;
}): JSX.Element {
  const [currentSlide, setCurrentSlide] = useState<number>(
    parseInt(getLocalStorageItem("currentSlide") || "1")
  );
  const { t } = useTranslation("common");
  const fileId = url?.split("/d/")[1]?.split("/")[0];
  const fileUrl = `https://docs.google.com/presentation/d/${fileId}/embed?start=false&rm=minimal&slide=${currentSlide}`;
  const getResourceData = getLocalStorageItem(KEYS.RESOURCE_DATA);
  const parsedData = getResourceData ? JSON.parse(getResourceData) : null;

  const handlePrevSlide = () => {
    setCurrentSlide((prevSlide) => {
      const newSlide = prevSlide > 1 ? prevSlide - 1 : prevSlide;
      localStorage.setItem(KEYS.CURRENT_SLIDE, newSlide.toString());
      return newSlide;
    });
  };

  const handleNextSlide = () => {
    setCurrentSlide((nextSlide) => {
      const newSlide = nextSlide < pageCount ? nextSlide + 1 : nextSlide;
      localStorage.setItem(KEYS.CURRENT_SLIDE, newSlide.toString());
      return newSlide;
    });
  };

  return (
    <div className="flex flex-col items-center">
      <div className="w-full  mb-2">
        {showTopic && (
          <>
            <p className="text-lg font-semibold mt-2 mb-2">
              {t("Topic")} : {parsedData.topic_name}
            </p>
            <p className="text-lg font-semibold mt-2 mb-2">
              {t("Name")} : {parsedData.name}
            </p>
          </>
        )}
      </div>
      <div className="relative w-full rounded-md overflow-hidden border border-gray-300">
        <iframe
          src={fileUrl}
          className="w-full h-[500px]"
          allowFullScreen
          title="Google Slides"
        />

        <div
          className="absolute inset-0 cursor-pointer"
          onClick={handleNextSlide}
        />
      </div>

      {/* Navigation */}
      <div className="flex justify-center items-center gap-4 mt-2">
        <button
          onClick={handlePrevSlide}
          disabled={currentSlide === 1}
          className={`px-4 py-2 rounded-lg text-white font-semibold transition-all ${
            currentSlide === 1
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-blue-500 hover:bg-blue-600"
          }`}
        >
          {t("Previous")}
        </button>

        <span className="text-lg font-medium">
          {t("Slide")} {currentSlide} {t("of")} {pageCount}
        </span>

        <button
          onClick={handleNextSlide}
          disabled={currentSlide === pageCount}
          className={`px-4 py-2 rounded-lg text-white font-semibold transition-all ${
            currentSlide === pageCount
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-blue-500 hover:bg-blue-600"
          }`}
        >
          {t("Next")}
        </button>
      </div>

      <div className="w-full flex justify-end ">
        <Button variant="outline" onClick={onCancel}>
          {t("Close")}
        </Button>
      </div>
    </div>
  );
}
