import { FC } from "react";
import { useTranslation } from "next-i18next";

const ActivityList: FC = () => {
  const activities = [
    "Completed Chapter 2 of React Basics",
    "Scored 85% in JavaScript Quiz",
    "Started Course: Advanced CSS Techniques",
  ];
  const { t } = useTranslation("common");
  return (
    <div className="bg-white p-4 rounded shadow">
      <h3 className="text-lg font-semibold mb-4"> {t("Recent Activities")}</h3>
      <ul className="space-y-2">
        {activities.map((activity, idx) => (
          <li key={idx} className="p-2 bg-gray-100 rounded">
            {activity}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ActivityList;
