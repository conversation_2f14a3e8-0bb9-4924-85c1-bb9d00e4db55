import { useCourse } from "@/hooks/useCourse";
import { generateBarColors } from "@/lib/constants";
import { SubjectMarksType } from "@/types";
import React, { useEffect, useState } from "react";
import { useTranslation } from "next-i18next";
import {
  Bar,
  BarChart,
  Cell,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  CartesianGrid,
  LabelList,
} from "recharts";
import { CardHeader, CardTitle, CardContent, Card } from "../ui/card";
import { Spinner } from "../ui/progressiveLoder";
interface MarkDetailsInterface {
  MarkDetails: SubjectMarksType[];
}

const MarksCardGraph = ({ MarkDetails }: MarkDetailsInterface) => {
  const { t } = useTranslation("common");

  const { getPerformance } = useCourse();
  const [isLoading, setIsLoading] = useState(true);
  const [markData, setMarkData] = useState<SubjectMarksType[]>([]);
  const [barColors, setBarColor] = useState<string[]>([]);
  const [focusBar, setFocusBar] = useState(null);

  useEffect(() => {
    if (MarkDetails && MarkDetails.length > 0) {
      const barData = MarkDetails?.filter((item) => item.marks > 0)
        .slice(0, 5)
        .map((item) => ({
          subject: item.subject,
          marks: item.marks,
        }));
      setMarkData(barData);
      setBarColor(generateBarColors(barData.length));
    }
    setIsLoading(false);
  }, [MarkDetails]);

  const hasData = markData && markData.length > 0;

  const CustomBarLabel = (props: any) => {
    const { x, y, value } = props;
    return (
      <text
        x={x + 10}
        y={y - 5}
        fill="#666"
        textAnchor="middle"
        className="text-sm font-medium"
      >
        {value}
      </text>
    );
  };

  const CustomSubjectLabels = () => {
    return (
      <div className="flex justify-end flex-col">
        {markData.map((item, index) => (
          <div key={index} className="flex items-center mb-1">
            <div
              className="w-3 h-3 mr-2 "
              style={{ backgroundColor: barColors[index] }}
            />
            <span className="text-xs text-gray-600">
              {" "}
              {/* Removed text-center and flex */}
              <span className="text-xs text-gray-600 text-center break-words">
                {item.subject.length > 35
                  ? `${item.subject.substring(0, 35)}...`
                  : item.subject}
              </span>
            </span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <>
      {!isLoading ? (
        <Card
          key={`bar-${1}`}
          className="bg-white border border-gray-200 rounded-lg w-full shadow-md"
        >
          <div className="bg-gray-600 h-2 w-full rounded-t-lg"></div>
          <CardHeader className="bg-gray-100">
            <CardTitle className="text-lg font-semibold text-gray-700">
              {t("Marks")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {hasData ? (
              <div>
                <ResponsiveContainer width="100%" height={240}>
                  <BarChart
                    data={markData}
                    barSize={25}
                    margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                  >
                    <XAxis
                      dataKey="subject"
                      axisLine={false}
                      tickLine={false}
                      tick={false}
                    />
                    <YAxis
                      stroke="#0088FE"
                      type="number"
                      domain={[0, 120]}
                      fontSize="12px"
                      tickCount={7}
                      dx={-10}
                    />
                    <Tooltip cursor={{ fill: "#fff" }} />
                    <CartesianGrid
                      strokeDasharray="3 3"
                      vertical={false}
                      stroke="#E5E7EB"
                    />
                    <Bar
                      dataKey="marks"
                      animationDuration={100}
                      animationBegin={1 * 200}
                      isAnimationActive={false}
                    >
                      <LabelList
                        dataKey="marks"
                        position="top"
                        content={CustomBarLabel}
                      />
                      {markData.map((_, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={barColors[index % barColors.length]}
                        />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
                <div className="flex justify-center items-center  -mt-[30px] h-20">
                  <CustomSubjectLabels />
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-[290px]">
                <p className="text-gray-500 text-lg font-medium pb-10">
                  {t("No data available")}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <Spinner />
      )}
    </>
  );
};

export default MarksCardGraph;
