import { FC } from "react";
import { useTranslation } from "next-i18next";
interface TableProps {
  data: { course: string; progress: number; grade: string }[];
}

const Table: FC<TableProps> = ({ data }) => {
  const { t } = useTranslation("common");
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white border border-gray-200">
        <thead>
          <tr>
            <th className="py-2 px-4 border-b">{t("Course")}</th>
            <th className="py-2 px-4 border-b">{t("Progress")}</th>
            <th className="py-2 px-4 border-b">{t("Grade")}</th>
          </tr>
        </thead>
        <tbody>
          {data.map((row, idx) => (
            <tr key={idx} className="text-center">
              <td className="py-2 px-4 border-b">{row.course}</td>
              <td className="py-2 px-4 border-b">{row.progress}%</td>
              <td className="py-2 px-4 border-b">{row.grade}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default Table;
