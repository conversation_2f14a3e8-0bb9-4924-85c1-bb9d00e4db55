import { AssignmentsType } from "@/types";
import { Play } from "lucide-react";
import React from "react";
import { useTranslation } from "next-i18next";

interface assignmentCard {
  AssignmentInfo: AssignmentsType;
}

const AssignmentCard = ({ AssignmentInfo }: assignmentCard) => {
  const [isHovered, setIsHovered] = React.useState(false);
  const { t } = useTranslation("common");
  return (
    <div
      className="relative group transition-transform duration-300 ease-in-out transform hover:scale-105"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative">
        <img
          src={AssignmentInfo.thumbnail}
          alt={AssignmentInfo.title}
          className="w-full h-48 object-cover rounded-lg"
        />
        {isHovered && (
          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
            <Play className="w-12 h-12 text-white" />
          </div>
        )}
      </div>
      <div className="mt-2 space-y-1">
        <h3 className="font-semibold text-lg">{AssignmentInfo.title}</h3>
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>{AssignmentInfo.subject}</span>
          <div className="flex items-center space-x-2">
            <span>{AssignmentInfo.duration}</span>
            <span>•</span>
            <span>
              {AssignmentInfo.views} {t("views")}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssignmentCard;
