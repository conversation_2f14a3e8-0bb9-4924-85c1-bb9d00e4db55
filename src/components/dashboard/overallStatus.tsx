import React, { FunctionComponent, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  Cell,
  XAxis,
  YA<PERSON><PERSON>,
  CartesianGrid,
  Label,
} from "recharts";
import { scaleOrdinal } from "d3-scale";
import { schemeCategory10 } from "d3-scale-chromatic";
import { Statistics, UserStatistics } from "@/types";
import { useTranslation } from "next-i18next";
const colors = scaleOrdinal(schemeCategory10).range();

const getPath = (x: number, y: number, width: number, height: number) => {
  return `M${x},${y + height}C${x + width / 3},${y + height} ${x + width / 2},${
    y + height / 3
  } 
  ${x + width / 2}, ${y}
  C${x + width / 2},${y + height / 3} ${x + (2 * width) / 3},${y + height} ${
    x + width
  }, ${y + height}
  Z`;
};

const TriangleBar: FunctionComponent<any> = (props: any) => {
  const { fill, x, y, width, height } = props;

  return <path d={getPath(x, y, width, height)} stroke="none" fill={fill} />;
};

export const OverallStats: React.FC<{ data: UserStatistics[] }> = ({
  data,
}) => {
  const chartData = data?.map((item) => ({
    name: item.course_name, // Use course name as the label
    uv: item.progress?.toFixed(2), // Use percent completed as the value (or total_marks)
  }));
  const { t } = useTranslation("common");
  return (
    <div className="w-full">
      <h2 className="text-2xl font-semibold text-center pt-4 mb-4">
        {t("Overall Statistics")}
      </h2>
      <BarChart
        width={1000} // Set this to a fixed width or dynamically calculate it
        height={300} // Increased height for better visibility
        data={chartData} // Use transformed data
        margin={{
          top: 20,
          right: 30,
          left: 30,
          bottom: 5,
        }}
        style={{ width: "100%" }} // Make the BarChart fill the width of the container
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis
          domain={[0, 100]} // Set the Y-axis range from 0 to 100%
          tickFormatter={(tick) => `${tick.toFixed(0)}%`} // Format ticks as percentages
        >
          <Label
            value="Percentage Completed"
            angle={-90}
            position="insideLeft" // Center the label vertically
            style={{ textAnchor: "middle", paddingBottom: 20 }}
          />
        </YAxis>
        <Bar
          dataKey="uv"
          fill="#8884d8"
          shape={<TriangleBar />}
          label={{ position: "top" }}
        >
          {chartData?.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
          ))}
        </Bar>
      </BarChart>
    </div>
  );
};
