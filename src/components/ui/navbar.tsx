"use client";
import { sidemenu } from "@/lib/constants";
import Link from "next/link";
import { MessageSquareMore, PhoneIcon, X, type LucideIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { LoginDataReturn } from "@/types";
import { Modal } from "@/components/ui/modal";
import ComfirmSubmit from "./confirmModal";
import { KEYS } from "@/lib/keys";
import ChangeTopic from "./topicModal";

interface MenuItemProps {
  MenuIcon: LucideIcon;
  name: string;
  route: string;
  color: string;
  onClick: () => void;
  onTap: (arg: boolean) => void;
}
interface SidebarProps {
  onTap: (val: boolean) => void;
}
const MenuItem = ({
  MenuIcon,
  name,
  route,
  color,
  onClick,
}: MenuItemProps): React.JSX.Element => {
  const pathname = usePathname();
  const isActive = pathname === route;
  const colorClass = isActive ? "text-black" : "text-white";

  return (
    <div
      className="relative"
      onClick={() => {
        onClick();
      }}
    >
      <Link href={route}>
        <div
          className={`flex items-center text-md py-3 border-b-white/10 ${colorClass} ${
            isActive &&
            "before:absolute before:bg-[#2894a0] before:w-[243px] before:h-14 before:-left-8 before:-top-1"
          }`}
        >
          <div className="text-xl flex w-[30px] z-10 mr-2">
            <MenuIcon color={color} />
          </div>
          <div className="z-10">{name}</div>
        </div>
      </Link>
    </div>
  );
};

export function Sidebar({ onTap }: SidebarProps): React.JSX.Element {
  const [sideBarMenu, setSideBarMenu] = useState(sidemenu);
  const [imageUrl, setImageUrl] = useState<string>();
  const [isLoading, setIsLoading] = useState(true);
  const pathname = usePathname();
  const [usersInfo, setUserInfo] = useState<LoginDataReturn>();
  const [openModal, setIsOpenModal] = useState<boolean>(false);
  const [openTopicModal, setIsOpenTopicModal] = useState<boolean>(false);
  const [topicName,setTopicName] = useState<string>('')
  const router = useRouter();
  useEffect(() => {
    let topic = localStorage.getItem(KEYS.SELECTED_TOPIC)
    setTopicName(topic as string)
    if (isLoading) {
      const userDetails = localStorage.getItem("userDetails");
      if (userDetails !== null && userDetails !== undefined) {
        const userInfo = JSON.parse(userDetails);
        setUserInfo(userInfo);
      }
      const profileImage = localStorage.getItem("profile_image");
      if (profileImage !== null) {
        const parsedData = JSON.parse(profileImage) as {
          avatar_url?: string;
        }[];
        const image_url = parsedData[0]?.avatar_url;
        if (image_url != null) {
          setImageUrl(image_url);
        } else {
          setImageUrl("/images/profile.png");
        }
      } else {
        setImageUrl("/images/profile.png");
      }
      setSideBarMenu(sidemenu);
    }
    setIsLoading(false);
  }, [isLoading]);

  const openLogoutModal = (name: string): void => {
    if (name === "Signout") setIsOpenModal(true);
  };

  const closeModal = (): void => {
    setIsOpenModal(!openModal);
  };
  const goToTopic = (): void => {
    router.push("/pages/topics");
  };
  const goToProfile = (): void =>{
    router.push("/pages/profile");
  }
  const closeTopicModal = (): void=> {
    setIsOpenTopicModal(false)
  }
const changeTopic = (): void =>{

  setIsOpenTopicModal(true)
}
  return (
    <div className="h-full">
      <div
        className="col-span-3 bg-[#00B9C7] lg:col-span-4 lg:border-r h-full w-56 overflow-y-auto custom-scrollbar"
        // style={{ backgroundColor: "#00B9C7" }}
      >
        <div className="p-2 flex items-center space-x-3 pt-5">
          <div className="relative w-10 h-10 rounded-full overflow-hidden">
            <img
              src={imageUrl}
              alt="Avatar"
              className="w-10 h-10 object-cover border-2 border-white rounded-full cursor-pointer"
              onClick={goToProfile}
            />
          </div>
          <div>
            <p className="text-white text-base font-semibold flex items-center">
              {usersInfo?.user_metadata.first_name}{" "}
              {usersInfo?.user_metadata.last_name}
              <span className="ml-12 block lg:hidden">
                <X className="cursor-pointer" onClick={() => onTap(false)} />
              </span>
            </p>

            <p className="text-white text-sm flex items-center">
              <PhoneIcon className="mr-1" size={14} />
              {usersInfo?.user_metadata.phone_number
                ? usersInfo.user_metadata.phone_number
                : usersInfo?.user_metadata.phonenumber1}
            </p>
          </div>
        </div>
        <div className="border border-white/50 rounded-lg p-2 mt-2 mx-2 flex items-center cursor-pointer" onClick={() => changeTopic()}>
          <MessageSquareMore className="text-white mr-2" size={16} />
          <p className="text-sm text-white pl-2">{topicName}</p>
        </div>
        <div className="space-y-4 py-4 border-t border-b border-white/50 mt-4">
          <div className="px-3 py-2">
            <div className="space-y-1">
              {sideBarMenu.map((item, index) => (
                <MenuItem
                  key={index}
                  MenuIcon={item.icon as LucideIcon}
                  name={item.name}
                  route={item.href}
                  color={item.color}
                  onClick={() => openLogoutModal(item.name)}
                  onTap={onTap}
                />
              ))}
            </div>
          </div>
        </div>
        <div className="py-4 flex justify-center items-center">
          <p className="text-white text-sm">v1.01</p>
        </div>
      </div>
      {openModal && (
        <Modal
          title={""}
          header=""
          openDialog={openModal}
          closeDialog={() => closeModal()}
          type="max-w-m mx-auto"
        >
          <ComfirmSubmit
            onSave={closeModal}
            onCancel={closeModal}
            isModal={true}
          />
        </Modal>
      )}
        {openTopicModal && (
        <Modal
          title={""}
          header=""
          openDialog={openTopicModal}
          closeDialog={() => closeTopicModal()}
          type="max-w-m mx-auto"
        >
          <ChangeTopic
            onSave={goToTopic}
            onCancel={ closeTopicModal}
            isModal={true}
          />
        </Modal>
      )}
    </div>
  );
}
