"use client";
import ReactPlayer from "react-player";
import { Button } from "@/components/ui/button";
import { getLocalStorageItem } from "@/lib/utils";
import { KEYS } from "@/lib/keys";
import { useEffect, useState } from "react";
import { extractVideoIdFromSearch } from "@/lib/constants";
import useComments from "@/hooks/useComments";
import { CommentsResponse } from "@/types";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Image from "next/image";
import { Card } from "./card";
import { useTranslation } from "next-i18next";
import emoji from "react-easy-emoji";
export default function VideoPlayer({
  url,
  onCancel,
  showTopic,
}: {
  onCancel: () => void;
  isModal?: boolean;
  url?: string;
  showTopic?: boolean;
}): JSX.Element {
  const handleCancelClick = (): void => {
    onCancel();
  };
  const { t } = useTranslation("common");
  const [fileUrl, setUrl] = useState<string>("");
  const { getComments } = useComments();
  const getResourceData = localStorage.getItem(KEYS.RESOURCE_DATA);
  const parsedData = getResourceData ? JSON.parse(getResourceData) : "";
  const instance_id = parsedData.id;
  const [commentsData, setCommentsData] = useState<CommentsResponse[]>([]);
  const [activeTab, setActiveTab] = useState("feedback");
  const [visibleComments, setVisibleComments] = useState<CommentsResponse[]>(
    []
  );
  const [showMore, setShowMore] = useState(false);
  const INITIAL_COMMENTS_COUNT = 3;
  const [feedbackComments, setFeedbackComments] = useState<CommentsResponse[]>(
    []
  );
  const [suggestionComments, setSuggestionComments] = useState<
    CommentsResponse[]
  >([]);
  useEffect(() => {
    const videoId = extractVideoIdFromSearch(url as string);
    if (videoId != null) {
      setUrl(`https://www.youtube.com/watch?v=${videoId}`);
    } else {
      setUrl(url as string);
    }
    if (showTopic) {
      getCommentData();
    }
  }, []);

  const formatDate = (isoDate: string): string => {
    const date = new Date(isoDate);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });
  };

  const getCommentData = () => {
    debugger;
    const fetchSessionData = async (): Promise<void> => {
      try {
        const result = await getComments(instance_id);

        const feedbacks = result.filter(
          (comment) => comment.type === "Feedback"
        );

        setFeedbackComments(feedbacks);

        const suggestions = result.filter(
          (comment) => comment.type === "Suggestion"
        );

        setSuggestionComments(suggestions);
        if (result) {
          // setCommentCount(result.length);
          setCommentsData(result);
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchSessionData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const toggleComments = () => {
    const currentComments =
      activeTab === "feedback" ? feedbackComments : suggestionComments;
    setVisibleComments(
      showMore
        ? currentComments.slice(0, INITIAL_COMMENTS_COUNT)
        : currentComments
    );
    setShowMore(!showMore);
  };

  const renderCommentsList = (comments: CommentsResponse[]) => {
    const sortedComments = [...comments].sort(
      (a, b) =>
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    );

    const visible = showMore
      ? sortedComments
      : sortedComments.slice(-INITIAL_COMMENTS_COUNT);

    const renderCommentItem = (
      item: CommentsResponse,
      level: number = 0
    ): JSX.Element => {
      const isReply = level > 0;

      return (
        <div
          key={item.id}
          className={`${
            isReply ? "ml-[30px] max-w-[calc(100%-30px)] ml-auto" : "w-full"
          } space-y-4`}
        >
          <Card className={`p-4 w-full mx-auto text-[var(--color-font-color)]`}>
            <div className="flex gap-4">
              {/* Avatar */}
              <div className="flex-none">
                <Image
                  src={
                    item.avatar_url?.startsWith("http://") ||
                    item.avatar_url?.startsWith("https://")
                      ? item.avatar_url
                      : "/assets/profile.png"
                  }
                  alt=""
                  width={45}
                  height={45}
                  className="rounded-full w-[45px] h-[45px] object-cover"
                />
              </div>

              {/* Text Content */}
              <div className="flex-1 space-y-2">
                <div className="flex items-center justify-between ">
                  <div className="font-semibold">{item.name}</div>
                </div>

                <div className="flex justify-between items-center ">
                  <div className="text-sm emoji">{emoji(item.message)}</div>
                  <div className="text-sm text-[var(--color-font-color)] ">
                    {formatDate(item.created_at)}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      );
    };
    return (
      <div className="space-y-4 w-full ">
        <div>
          {comments.length > 0 ? (
            <>
              <div className="max-h-[400px] overflow-y-auto rounded-md overflow-hidden text-[var(--color-font-color)]">
                <div className="space-y-4 p-1">
                  {visible.map((item) => renderCommentItem(item))}
                </div>
              </div>

              {comments.length > INITIAL_COMMENTS_COUNT && (
                <div className="flex justify-center ">
                  <Button
                    onClick={toggleComments}
                    variant="outline"
                    className="w-full sm:w-auto "
                  >
                    {showMore
                      ? "Show Less"
                      : `Show ${
                          comments.length - INITIAL_COMMENTS_COUNT
                        } More Comments`}
                  </Button>
                </div>
              )}
            </>
          ) : (
            <div className="flex h-20 items-center justify-center text-[var(--color-font-color)] ">
              No comments found
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      <div className="w-full  mb-2">
        {showTopic && (
          <>
            <p className="text-lg font-semibold mt-2 mb-2">
              Topic: {parsedData.topic_name}
            </p>
            <p className="text-lg font-semibold mt-2 mb-2">
              Name: {parsedData.name}
            </p>
          </>
        )}
      </div>
      <div className="w-full h-0 relative py-60 rounded-md overflow-hidden">
        <ReactPlayer
          style={{
            position: "absolute",
            top: "0",
            left: "0",
          }}
          onPause={() => console.log("res")}
          width="100%"
          height="100%"
          url={fileUrl}
          controls={true}
        />
      </div>

      {commentsData.length > 0 && (
        <div className="rounded-lg border bg-card p-4 text-[var(--color-font-color)]">
          <Tabs defaultValue="feedback" onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2 text-[var(--color-font-color)]">
              <TabsTrigger value="feedback">
                Feedback ({feedbackComments?.length})
              </TabsTrigger>
              <TabsTrigger value="suggestion">
                Suggestions ({suggestionComments?.length})
              </TabsTrigger>
            </TabsList>
            <div className="mt-4">
              <TabsContent value="feedback">
                {renderCommentsList(feedbackComments)}
              </TabsContent>
              <TabsContent value="suggestion">
                {renderCommentsList(suggestionComments)}
              </TabsContent>
            </div>
          </Tabs>
        </div>
      )}

      {/* Button aligned to the right */}
      <div className="flex justify-end gap-4 mt-4">
        <Button
          type="submit"
          className=""
          variant="outline"
          onClick={handleCancelClick}
        >
          {t("Close")}
        </Button>
      </div>
    </div>
  );
}
