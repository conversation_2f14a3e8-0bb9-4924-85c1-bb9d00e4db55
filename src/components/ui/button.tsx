import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-[var(--color-button-primary)] text-[var(--color-button-primary-text)] border border-black ",
        destructive: "bg-[var(--color-button-dismiss)] text-[var(--color-button-dismiss-text)] border border-black ",
        outline: "bg-[var(--color-button-dismiss)] text-[var(--color-button-dismiss-text)] border border-black ",
        secondary: "bg-[var(--color-button-secondary)] text-[var(--color-button-secondary-text)] border border-black",
        ghost: "bg-[var(--color-button-primary)] text-[var(--color-button-primary-text)] border border-black",
        link: "bg-[var(--color-button-primary)] text-[var(--color-button-primary-text)] border border-black",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)


export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
   

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);


Button.displayName = "Button"

export { Button, buttonVariants }
