import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { SUCCESS_MESSAGES } from "@/lib/messages";
import { useTranslation } from "next-i18next";

export default function ChangeTopic({
  onCancel,
  onSave,
}: {
  onSave: () => void;
  onCancel: () => void;
  isModal?: boolean;
}): React.JSX.Element {
  const handleLogoutClick = (): void => {
    onSave();
  };
  const { t } = useTranslation("common");
  return (
    <div className="rounded p-4">
      <div className="mb-2 text-center text-lg">
        <p>{t("topicChangeMsg")}</p>
      </div>
      <div className="flex justify-center items-center space-x-6 mt-4">
        <Button
          type="button"
          variant="outline"
          className="primary w-24 h-10 rounded-3xl"
          onClick={onCancel}
        >
          {t("No")}
        </Button>
        <Button
          type="submit"
          className="w-24 h-10 rounded-3xl"
          onClick={handleLogoutClick}
          variant="default"
        >
          {t("Yes")}
        </Button>
      </div>
    </div>
  );
}
