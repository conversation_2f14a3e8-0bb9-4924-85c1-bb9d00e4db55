"use client";
import { <PERSON><PERSON>r, MenubarMenu } from "../../components/ui/menuBar";

import React, { useEffect, useRef, useState } from "react";
import { AlignJustify, BellIcon, BellRing, SidebarCloseIcon, SidebarOpenIcon } from "lucide-react";
import NotificationMenu from "./user-notifications";

export function Menu({
  onTap,
  title,
  showMenuBar
}: {
  onTap: (val: boolean) => void;
  title: string;
  showMenuBar: boolean
}): React.JSX.Element {
  const [open, setOpen] = useState(true);
  const buttonRef = useRef(null);
  const handleResize = () => {
    if (window.innerWidth < 1024) {
      setOpen(false);
    } else {
      setOpen(true);
    }
  };

  useEffect(() => {
    // Set initial state based on window size
    handleResize();

    // Add resize event listener
    window.addEventListener('resize', handleResize);

    // Cleanup the event listener on component unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []); // Empty dependency array to ensure this runs only once

  return (
    <Menubar
      className={`${
        open && showMenuBar
          ? "ml-0 fixed #96dda5 flex w-[100%] lg:ml-56 fixed flex lg:w-[calc(100%-224px)]"
          : "ml-0 fixed #96dda5 flex w-[100%] "
      } z-30 justify-between items-center rounded-none border-b border-none px-2 lg:py-4 py-2 h-14 transition-all duration-500 transform  border-b border-white/20 bg-white/80 `}
     
    >
    
     <MenubarMenu>
        <div className="flex lg:justify-start items-center w-[25%] hidden lg:flex">
        {showMenuBar &&(  <AlignJustify
              color="#000"
              ref={buttonRef}
              className="mx-2 z-50 cursor-pointer"
              onClick={() => {
                onTap(!open);
                setOpen(!open);
              }}
            /> 
        )}
        </div>
        <div className="flex lg:justify-start items-center w-[25%] block lg:hidden">
        
            {showMenuBar && ( <AlignJustify
              color="#000"
              ref={buttonRef}
              className="mx-2 z-50 cursor-pointer"
              onClick={() => {
                onTap(true);
                setOpen(true);
              }}
            />)}
        
        </div>
      </MenubarMenu>
    
      <div className="flex-grow text-center">
        <span className="text-black font-semibold sm:text-xl md:text-2xl lg:text-2xl">{title}</span>
      </div>
      <div className="w-[25%]"></div>
      <MenubarMenu>
      <NotificationMenu></NotificationMenu>
      </MenubarMenu>
    </Menubar>
    
    
  );
}
