"use client";
import { But<PERSON> } from "@/components/ui/button";
import React from "react";
import { useTranslation } from "react-i18next";
const SkipModal = ({
  closeDialog,
  proceedSkip,
}: {
  closeDialog: () => void;
  proceedSkip: () => void;
}): React.JSX.Element => {
  const closeModal = () => {
    closeDialog();
  };
  const { t } = useTranslation();
  return (
    <div className="">
      <p className="text-center mb-4 text-lg font-semibold">
        {t("skipConfirmation")}
      </p>

      <div className="flex justify-center gap-4">
        <Button
          className="w-full sm:w-auto "
          onClick={() => {
            proceedSkip();
          }}
        >
          {t("Yes")}
        </Button>
        <Button type="submit" onClick={closeModal} variant="outline">
          {t("No")}
        </Button>
      </div>
    </div>
  );
};
export default SkipModal;
