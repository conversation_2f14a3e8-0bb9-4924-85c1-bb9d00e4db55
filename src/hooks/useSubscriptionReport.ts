import { rpc, views } from "@/lib/apiConfig";
import { supabase } from "@/lib/client";
import { subscriptionReportResponse } from "@/types";



interface UseSubscriptionReturn {
    subscriptionReport: (orgId: string) => Promise<subscriptionReportResponse>;
}
const useSubscriptionReport = (): UseSubscriptionReturn => {
async function subscriptionReport(orgId: string): Promise<subscriptionReportResponse> {
    try {
        const requestBody = {
            org_id : orgId
          };
        const { data, error } = await supabase.rpc<string, null>(
          rpc.getSubscriptionReport,
          requestBody
        );
        if (error) {
          throw new Error(error.details);
        }
      
          return data as subscriptionReportResponse;
        
      } catch (error) {
        throw error;
      }
  }
  return {
    subscriptionReport,
  };
}
export default useSubscriptionReport;