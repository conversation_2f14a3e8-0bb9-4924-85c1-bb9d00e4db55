
import { LiveClassResponse, LiveClassReturn  } from "@/types";
import { supabase } from "../lib/client";
import { rpc } from "@/lib/apiConfig";

interface UseLiveClassReturn {
  getMeetingDetails: (
    queryParams: LiveClassReturn
  ) => Promise<LiveClassResponse[]>; 
}

export const UseLiveClass = (): UseLiveClassReturn => {
  async function getMeetingDetails(
    queryParams: LiveClassReturn
  ): Promise<LiveClassResponse[]> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.getLiveClassDetails,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as LiveClassResponse[];
  }
  return {
    getMeetingDetails,
  };
};
