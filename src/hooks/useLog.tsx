import { LiveClassResponse, LoginUserData, UserLogResponse } from "@/types";
import { supabase } from "../lib/client";
import { rpc } from "@/lib/apiConfig";
import { KEYS } from "@/lib/keys";
import { getLocalStorageItem } from "@/lib/utils";
import { UUID } from "@/lib/constants";

interface UseLogClassReturn {
  insertLogDetails: (
    type: string,
    screen: string,
    action: string,
    status: string,
    targetId: string | null
  ) => Promise<UserLogResponse>;
}

export const UseLogClass = (): UseLogClassReturn => {
  const USER_DATA = getLocalStorageItem(KEYS.USER_DETAILS);
  const userInfo = JSON.parse(USER_DATA ?? "{}") as LoginUserData;
  // const userId = localStorage.getItem(KEYS.USER_ID);

  async function insertLogDetails(
    type: string,
    screen: string,
    action: string,
    status: string,
    targetId: string | null
  ): Promise<UserLogResponse> {
    const isLoginScreen = screen === "Login";
    const resolvedUserId = isLoginScreen
      ? targetId && targetId !== "null"
      ? targetId
      : UUID
      : userInfo.id && userInfo.id  !== "null"
      ? userInfo.id 
      : UUID;

    const reqParams = {
      org_id: localStorage.getItem(KEYS.ORG_ID) ?? UUID,
      activity_type: type,
      screen_name: screen,
      action_details: action,
      target_id: targetId && targetId !== "null" ? targetId : UUID,
      session_id: localStorage.getItem(KEYS.ACCESS_TOKEN) ?? "",
      action_comment: action + " at " + new Date().toLocaleString(),
      user_agent: navigator.userAgent,
      log_source: "Web",
      log_result: status,
      user_id: resolvedUserId,
    };
    const { data, error } = await supabase.rpc<string, null>(
      rpc.logUserActivity,
      reqParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as UserLogResponse;
  }
  return {
    insertLogDetails,
  };
};
