import {
  AttemptedQuizRequest,
  AttemptedQuizResponse,
  AttemptedQuizType,
  CheckExamDetailsRequest,
  CheckExamDetailsResponse,
  CheckPointsResponse,
  CommentsResponse,
  EndCheckPointResponse,
  ErrorType,
  ExamQuestionType,
  ExamRankListRequest,
  ExamReviewType,
  QuizGradeResponse,
  QuizRequest,
  QuizResponse,
  SubmitAnswerType,
  SubmitQuizResponseType,
  SubmitQuizType,
} from "@/types";
import {
  ExamQuestionsResult,
  ExamViewRequest,
  ExamViewResult,
  RankListRequest,
  RankListResponse,
} from "@/types";
import { supabase } from "../lib/client";
import { rpc, views } from "@/lib/apiConfig";
import { KEYS } from "@/lib/keys";
import { ERROR_MESSAGES } from "@/lib/messages";

interface UseExamReturn {
  getExamIntro: (quiz_id: string) => Promise<ExamQuestionsResult[]>;
  getCourseQuizes: (params: QuizRequest) => Promise<QuizResponse[]>;
  getAtemptedQuizesOfUser: (
    params: AttemptedQuizRequest
  ) => Promise<AttemptedQuizResponse[]>;
  startExam: (queryParams: ExamViewRequest) => Promise<ExamViewResult>;
  // getExamQuestions: (quiz_id: string) => Promise<ExamQuestionsResult[]>;
  submitQuiz: (examData?: SubmitQuizType) => Promise<SubmitQuizResponseType>;
  evaluateAnswer: (examData?: SubmitQuizType) => Promise<ExamReviewType[]>;
  getCourseRankList: (params: RankListRequest) => Promise<RankListResponse>;
  getExamRankList: (params: ExamRankListRequest) => Promise<RankListResponse>;
  calculateQuizGrade: (quiz_attempt_id: string) => Promise<QuizGradeResponse>;
  submitAnswers: (params: SubmitAnswerType) => Promise<QuizGradeResponse[]>;
  examResultAnalysis: (
    params: AttemptedQuizType
  ) => Promise<AttemptedQuizResponse[]>;
  getCheckPointsdetails: (
    course_module_id: string
  ) => Promise<CheckPointsResponse>;
  startCheckPointQuiz: (
    queryParams: CheckExamDetailsRequest
  ) => Promise<CheckExamDetailsResponse[]>;
  endCheckPointQuiz: (
    queryParams: SubmitQuizType
  ) => Promise<EndCheckPointResponse>;
}

export const useExam = (): UseExamReturn => {
  async function getExamIntro(quiz_id: string): Promise<ExamQuestionsResult[]> {
    try {
      const requestBody = {
        quiz_id: quiz_id,
      };
      const { data, error } = await supabase.rpc<string, null>(
        rpc.getQuestionOfQuiz,
        requestBody
      );
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }
      if (Array.isArray(data) && data.length > 0) {
        return data as ExamQuestionsResult[];
      } else {
        return [] as ExamQuestionsResult[];
      }
    } catch (error) {
      throw error;
    }
  }

  async function getCourseQuizes(params: QuizRequest): Promise<QuizResponse[]> {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.getQuizesOfCourse,
        params
      )) as { data: QuizResponse[]; error: ErrorType | null };
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }
      return data as QuizResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getAtemptedQuizesOfUser(
    params: AttemptedQuizRequest
  ): Promise<AttemptedQuizResponse[]> {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.getAttemptedQuizesOfUser,
        params
      )) as { data: AttemptedQuizResponse[]; error: ErrorType | null };
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }
      return data as AttemptedQuizResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function startExam(
    queryParams: ExamViewRequest
  ): Promise<ExamViewResult> {
    try {
      const { data, error } = await supabase.rpc<string, null>(
        rpc.startExam,
        queryParams
      );
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }
      if (data) {
        // Assuming data is not an array but a single result object
        return data as ExamViewResult; // Convert to array for consistency
      } else {
        return {} as ExamViewResult; // Return empty array if no data is returned
      }
    } catch (error: any) {
      throw error.message;
    }
  }
  // async function getExamQuestions(
  //   quiz_id: string
  // ): Promise<ExamQuestionsResult[]> {
  //   try {
  //     let reqParams = {
  //       quiz_id: quiz_id,
  //     };
  //     const { data, error } = await supabase.rpc<string, null>(
  //       rpc.getQuestionOfQuiz,
  //       reqParams
  //     );
  //     if (error) {
  //       throw new Error(error.details);
  //     }
  //     if (Array.isArray(data) && data.length > 0) {
  //       return data as ExamQuestionsResult[];
  //     } else {
  //       return [] as ExamQuestionsResult[];
  //     }
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  async function submitQuiz(
    examData?: SubmitQuizType
  ): Promise<SubmitQuizResponseType> {
    const params = examData;
    try {
      const { data, error } = (await supabase.rpc(rpc.submitExam, params)) as {
        data: SubmitQuizType;
        error: ErrorType | null;
      };
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }
      return data as SubmitQuizResponseType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function evaluateAnswer(
    examData?: SubmitQuizType
  ): Promise<ExamReviewType[]> {
    const params = examData;
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getQuizRightAnswers,
        params
      )) as {
        data: ExamReviewType[];
        error: ErrorType | null;
      };
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }
      return data as ExamReviewType[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function calculateQuizGrade(
    attempt_id: string
  ): Promise<QuizGradeResponse> {
    let params = {
      quiz_attempt_id: attempt_id,
    };

    try {
      const { data, error } = (await supabase.rpc(
        rpc.calculateQuizGrade,
        params
      )) as {
        data: QuizGradeResponse;
        error: ErrorType | null;
      };
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }
      return data as QuizGradeResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function submitAnswers(
    queryParams: SubmitAnswerType
  ): Promise<QuizGradeResponse[]> {
    try {
      const attendExam = views?.submitAnswers;

      const exeQuery = supabase
        .from(attendExam)
        .select()
        .eq("quiz_id", queryParams.quiz_id)
        .eq("quiz_attempt_id", queryParams.quiz_attempt_id);

      const { data, error } = await exeQuery;
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }
      return data as QuizGradeResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function examResultAnalysis(
    reqParams: AttemptedQuizType
  ): Promise<AttemptedQuizResponse[]> {
    let params = {
      user_id: reqParams.user_id,
      org_id: reqParams.org_id,
      course_id: reqParams.course_id,
      type_of_quiz:reqParams.type_of_quiz,
    };

    try {
      const { data, error } = (await supabase.rpc(
        rpc.resultAnalysis,
        params
      )) as {
        data: AttemptedQuizResponse[];
        error: ErrorType | null;
      };
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }
      return data as AttemptedQuizResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getCourseRankList(
    params: RankListRequest
  ): Promise<RankListResponse> {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.fetchRankListForCourse,
        params
      )) as { data: RankListResponse; error: ErrorType | null };
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }
      return data as RankListResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getExamRankList(
    params: ExamRankListRequest
  ): Promise<RankListResponse> {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.fetchRankListForQuiz,
        params
      )) as { data: RankListResponse; error: ErrorType | null };
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }
      return data as RankListResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function getCheckPointsdetails(
    course_module_id: string
  ): Promise<CheckPointsResponse> {
    try {
      const org_id = localStorage.getItem(KEYS.ORG_ID);
      const requestBody = {
        course_module_id: course_module_id,
        org_id: org_id,
        is_admin_panel: false
      };
      const { data, error } = await supabase.rpc<string, null>(
        rpc.getCheckpoints,
        requestBody
      );
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }

      return data as CheckPointsResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function startCheckPointQuiz(
    queryParams: CheckExamDetailsRequest
  ): Promise<CheckExamDetailsResponse[]> {
    try {
      const { data, error } = await supabase.rpc<string, null>(
        rpc.startQuiz,
        queryParams
      );
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }

      return data as CheckExamDetailsResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function endCheckPointQuiz(
    queryParams: SubmitQuizType
  ): Promise<EndCheckPointResponse> {
    try {
      const { data, error } = await supabase.rpc<string, null>(
        rpc.endQuiz,
        queryParams
      );
      if (error) {
        if (error.message === "TypeError: Failed to fetch") {
          debugger;
          throw Error(ERROR_MESSAGES.service_unreach_msg);
        } else {
          throw new Error(error.details);
        }
      }

      return data as EndCheckPointResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getExamIntro,
    startExam,
    // getExamQuestions,
    submitQuiz,
    getCourseQuizes,
    getAtemptedQuizesOfUser,
    evaluateAnswer,
    getCourseRankList,
    getExamRankList,
    calculateQuizGrade,
    submitAnswers,
    examResultAnalysis,
    getCheckPointsdetails,
    startCheckPointQuiz,
    endCheckPointQuiz,
  };
};
