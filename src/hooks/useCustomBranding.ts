import { supabase } from "@/lib/client";
import { rpc } from "@/lib/apiConfig";
import type { ErrorType, CustomBrandingDetails } from "@/types";

interface useCustomBrandingReturn {
  getCustomBrandingDetails: (orgId: string) => Promise<CustomBrandingDetails>;
 
}

const useCustomBranding = (): useCustomBrandingReturn => {

  async function getCustomBrandingDetails(
    orgId: string,
  ): Promise<CustomBrandingDetails> {
    try {
      const params = { org_id: orgId };

      const { data, error } = (await supabase.rpc(
        rpc.getCustomBrandingDetails,
        params,
      )) as { data: CustomBrandingDetails; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as CustomBrandingDetails;
    } catch (error) {
      console.error("Error", error);
      throw error;
    }
  }
  return {  getCustomBrandingDetails };
};

export default useCustomBranding;
