import { NotificationRequest, NotificationResponse } from "@/types";
import { supabase } from "../lib/client";
import { rpc } from "@/lib/apiConfig";

interface UseNotificationReturn {
  getNotifications: (
    queryParams: NotificationRequest
  ) => Promise<NotificationResponse[]>;
}

export const UseNotification = (): UseNotificationReturn => {
  async function getNotifications(
    queryParams: NotificationRequest
  ): Promise<NotificationResponse[]> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.getAllNotifiation,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as NotificationResponse[];
  }
  return {
    getNotifications,
  };
};
