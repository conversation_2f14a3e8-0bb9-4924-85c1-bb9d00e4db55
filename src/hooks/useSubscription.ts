import {
  AddSubscriptionRequest,
  AddSubscriptionResult,
  CourseResourceReturn,
  ResourceList,
  SubscriptionReturnType,
  SubscriptionStatusResult,
} from "@/types";

import { supabase } from "../lib/client";
import { rpc, views } from "@/lib/apiConfig";

interface UseSubscriptionReturn {
  getSubscriptions: (
    org_id: string,
    user_id: string
  ) => Promise<SubscriptionReturnType>;

  addSubscriptionPlan: (
    data: AddSubscriptionRequest
  ) => Promise<AddSubscriptionResult>;

  getPlanStatus: (
    data: AddSubscriptionRequest
  ) => Promise<SubscriptionStatusResult>;
  getResourceList: (
    org_id: string,
    plan_id: string
  ) => Promise<CourseResourceReturn>;
}

export const useSubscription = (): UseSubscriptionReturn => {
  async function getSubscriptions(
    org_id: string,
    user_id: string
  ): Promise<SubscriptionReturnType> {
    try {
      const requestBody = {
        org_id: org_id,
        user_id: user_id,
      };
      const { data, error } = await supabase.rpc<string, null>(
        rpc.getSubscription,
        requestBody
      );
      if (error) {
        throw new Error(error.details);
      }
      return data as SubscriptionReturnType;
    } catch (error) {
      throw error;
    }
  }
  async function addSubscriptionPlan(
    passData: AddSubscriptionRequest
  ): Promise<AddSubscriptionResult> {
    try {
      const requestBody = passData;
      const { data, error } = await supabase.rpc<string, null>(
        rpc.addSubscription,
        requestBody
      );
      if (error) {
        throw new Error(error.details);
      }
      return data as AddSubscriptionResult;
    } catch (error) {
      throw error;
    }
  }
  async function getPlanStatus(
    passData: AddSubscriptionRequest
  ): Promise<SubscriptionStatusResult> {
    try {
      const requestBody = passData;
      const { data, error } = await supabase.rpc<string, null>(
        rpc.getPlanPurchaseStatus,
        requestBody
      );
      if (error) {
        throw new Error(error.details);
      }
      return data as SubscriptionStatusResult;
    } catch (error) {
      throw error;
    }
  }
  async function getResourceList(
    orgId: string,
    planId: string
  ): Promise<CourseResourceReturn> {
    try {
      const requestBody = {
        org_id: orgId,
        plan_id: planId
         }
      const { data, error } = await supabase.rpc<string, null>(
        rpc.getResourcesListByPlan,
        requestBody
      );
      if (error) {
        throw new Error(error.details);
      }
      return data as CourseResourceReturn;
    } catch (error) {
      throw error;
    }
  }
  return {
    getSubscriptions,
    addSubscriptionPlan,
    getPlanStatus,
    getResourceList
  };
};
