
"use client";
import React, { useEffect, useState } from "react";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import ErrorBoundary from "@/components/errorBoundary";
import { ThemeProvider } from "@/context/ThemeContext";
import TranslationProvider from "@/lib/translationProvider";
import i18n from "i18next"; 

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const [lang, setLang] = useState("en");

  useEffect(() => {
    const updateHtmlAttributes = (language: string) => {
      const isArabic = language === "ar";
      setLang(isArabic ? "ar" : "en");
      document.documentElement.setAttribute("lang", language);
      document.documentElement.setAttribute("dir", isArabic ? "rtl" : "ltr");
    };

    // Initial set
    const initialLang = i18n.language || "en";
    updateHtmlAttributes(initialLang);

    // ✅ Subscribe to language change
    i18n.on("languageChanged", updateHtmlAttributes);

    // Clean up
    return () => {
      i18n.off("languageChanged", updateHtmlAttributes);
    };
  }, []);

  return (
    <html lang={lang} dir={lang === "ar" ? "rtl" : "ltr"}>
      <body>
        <ErrorBoundary>
          <ThemeProvider>
            <TranslationProvider>{children}</TranslationProvider>
          </ThemeProvider>
        </ErrorBoundary>
        <Toaster />
      </body>
    </html>
  );
}
