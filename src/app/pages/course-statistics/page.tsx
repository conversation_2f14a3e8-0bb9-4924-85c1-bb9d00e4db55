"use client";
"use client";
import React, { useEffect, useState } from "react";
import { Spinner } from "@/components/ui/progressiveLoder";
import { getLocalStorageItem } from "@/lib/utils";
import { useCourse } from "@/hooks/useCourse";
import { KEYS } from "@/lib/keys";
import { UserStatistics } from "@/types";
import { secondsToTime, timeToSeconds } from "@/lib/timeUtils";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import MainLayout from "../layouts/mainLayout";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useTranslation } from "next-i18next";
import {
  ACHIEVEMENTS,
  ACQUIRED_MARKS,
  COVERED_PERCENTAGE,
  COVERED_TIME,
} from "@/lib/constants";

const AllCourseDetails = () => {
  const { t } = useTranslation("common");
  const { getUserCourseStatics } = useCourse();
  const orgID = getLocalStorageItem(KEYS.ORG_ID);
  const [isLoading, setIsLoading] = useState(false);
  const [userStatistics, setUserStatics] = useState<UserStatistics[]>([]);
  const [dashboardStats, setDashboardStats] = useState<any>(null);
  const router = useRouter();
  useEffect(() => {
    getStatisticsDetails();
  }, []);

  const getStatisticsDetails = async (): Promise<void> => {
    try {
      let user_id = getLocalStorageItem(KEYS.USER_ID) || "";
      const response = await getUserCourseStatics({
        org_id: orgID as string,
        course_id: null,
        user_id: user_id as string,
      });

      setIsLoading(true);
      const sortedResponse = response.sort((a, b) => {
        if (a.course_name.toLowerCase() < b.course_name.toLowerCase()) {
          return -1;
        }
        if (a.course_name.toLowerCase() > b.course_name.toLowerCase()) {
          return 1;
        }
        return 0;
      });

      setUserStatics(sortedResponse);
      const totalMarks = response.reduce(
        (acc, course) => acc + (course.totalMarks || 0),
        0
      );
      const totalAchievements = response.reduce(
        (acc, course) => acc + course.achievements,
        0
      );
      const totalTimeInSeconds = response.reduce(
        (acc, course) => acc + timeToSeconds(course.time_spent as string),
        0
      );
      const totalProgress = response.reduce(
        (acc, course) => acc + course.progress,
        0
      );
      const totalPercentCovered = totalProgress / response.length;
      const totalTimeFormatted = secondsToTime(totalTimeInSeconds);

      setDashboardStats({
        totalMarksGot: totalMarks,
        totalAchievements,
        totalTime: totalTimeFormatted,
        totalProgress: totalPercentCovered.toFixed(2),
      });
    } catch (error) {
      setIsLoading(true);
    }
  };

  // Custom template for progress column
  const progressTemplate = (rowData: UserStatistics) => {
    const percentage = rowData.progress;
    const getProgressColor = (value: number) => {
      if (value >= 75) return "bg-green-500";
      if (value >= 50) return "bg-yellow-500";
      return "bg-red-500";
    };

    return (
      <div className="flex items-center gap-2">
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`${getProgressColor(
              percentage
            )} h-2 rounded-full transition-all duration-300`}
            style={{ width: `${percentage}%` }}
          />
        </div>
        <span className="text-sm font-medium">{percentage}%</span>
      </div>
    );
  };

  // Custom template for achievements column
  const achievementsTemplate = (rowData: UserStatistics) => {
    return (
      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
        {rowData.achievements}
      </span>
    );
  };

  return (
    <MainLayout titleText={""}>
      {isLoading ? (
        <main className="flex-1">
          <div className=" mx-auto p-6">
            {/* Table Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <DataTable
                value={userStatistics}
                className="border-none"
                rowHover
                stripedRows
                showGridlines={false}
                emptyMessage="No courses found"
              >
                <Column
                  field="course_name"
                  header="Course Name"
                  className="px-6 py-4 text-sm text-gray-900"
                  headerClassName="px-6 py-4 text-sm font-semibold text-gray-900 bg-gray-50"
                />
                <Column
                  field="totalMarks"
                  header={ACQUIRED_MARKS}
                  className="px-6 py-4 text-sm text-gray-900"
                  headerClassName="px-6 py-4 text-sm font-semibold text-gray-900 bg-gray-50"
                />
                <Column
                  field="progress"
                  header={COVERED_PERCENTAGE}
                  body={progressTemplate}
                  className="px-6 py-4 text-sm text-gray-900"
                  headerClassName="px-6 py-4 text-sm font-semibold text-gray-900 bg-gray-50"
                />
                <Column
                  field="time_spent"
                  header={COVERED_TIME}
                  className="px-6 py-4 text-sm text-gray-900"
                  headerClassName="px-6 py-4 text-sm font-semibold text-gray-900 bg-gray-50"
                />
                <Column
                  field="achievements"
                  header={ACHIEVEMENTS}
                  body={achievementsTemplate}
                  className="px-6 py-4 text-sm text-gray-900"
                  headerClassName="px-6 py-4 text-sm font-semibold text-gray-900 bg-gray-50"
                />
              </DataTable>
            </div>

            {/* Consolidated Results Section */}
            <div className="mt-6 bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  {t("Consolidated Result")}
                </h3>
              </div>
              <div className="divide-y divide-gray-200">
                <div className="flex justify-between px-6 py-4">
                  <span className="text-sm font-medium text-gray-900">
                    {t("Total Marks")}
                  </span>
                  <span className="text-sm text-gray-700">
                    {dashboardStats?.totalMarksGot}
                  </span>
                </div>
                <div className="flex justify-between px-6 py-4">
                  <span className="text-sm font-medium text-gray-900">
                    {t("Total Achievements")}
                  </span>
                  <span className="text-sm text-gray-700">
                    {dashboardStats?.totalAchievements}
                  </span>
                </div>
                <div className="flex justify-between px-6 py-4">
                  <span className="text-sm font-medium text-gray-900">
                    {t("Total Time")}
                  </span>
                  <span className="text-sm text-gray-700">
                    {dashboardStats?.totalTime}
                  </span>
                </div>
                <div className="flex justify-between px-6 py-4">
                  <span className="text-sm font-medium text-gray-900">
                    {t("Average Progress")}
                  </span>
                  <span className="text-sm text-gray-700">
                    {dashboardStats?.totalProgress}%
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-end mt-4">
            <Button
              variant="outline"
              className="w-ful rounded-md"
              onClick={() => {
                router.push(`/pages/dashboard`);
              }}
            >
              {t("Back")}
            </Button>
          </div>
        </main>
      ) : (
        <Spinner />
      )}
    </MainLayout>
  );
};

export default AllCourseDetails;
