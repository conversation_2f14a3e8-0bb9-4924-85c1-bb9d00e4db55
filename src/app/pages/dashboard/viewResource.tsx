"use client";
import React, { useEffect, useState } from "react";
import { Carousel } from "primereact/carousel";
import {
  AllResourceResponse,
  SkipVideoRequest,
  ToastType,
  ViewResourcePageType,
} from "@/types";
import { useCourse } from "@/hooks/useCourse";
import { KEYS } from "@/lib/keys";
import { useRouter } from "next/navigation";
import { AlertCircle, FileIcon, IterationCw, VideoIcon } from "lucide-react";
import { Badge } from "primereact/badge";
import { useTranslation } from "next-i18next";
import {
  DATE_FORMAT,
  mobResponsive,
  pptCompletionAlert,
} from "@/lib/constants";
import moment from "moment";
import { Modal } from "@/components/ui/modal";
import VideoDialog from "@/components/ui/video-dialog";
import DocumentDialog from "@/components/pptDialog";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import SkipModal from "@/components/skip-video-dialog";
import ImageDialog from "@/components/ui/imageDialog";
import { UseLogClass } from "@/hooks/useLog";

interface VideoResourcesProps {
  resources: AllResourceResponse[];
  title: string;
  isModal: boolean;
  hideProgress: boolean;
  isGeneralResource?: boolean;
}
export default function ViewResources({
  resources,
  title,
  isModal,
  hideProgress,
  isGeneralResource,
}: VideoResourcesProps): React.JSX.Element {
  const { t } = useTranslation("common");
  const [data, setData] = useState<AllResourceResponse[]>(resources);
  const { viewResourcePage, skipResource } = useCourse();
  const [resourceType, setResourceType] = useState("");
  const [url, setUrl] = useState("");
  const router = useRouter();
  const { toast } = useToast() as unknown as ToastType;
  const showIndicators = data?.length > 5;
  const [viewOpen, setViewOpen] = React.useState(false);
  const [viewOpenPPT, setViewOpenPPT] = React.useState(false);
  const [viewOpenImage, setViewOpenImage] = React.useState(false);
  const [pageCount, setPageCount] = React.useState<number>(0);
  const [openSkipModal, setOpenSkipModal] = React.useState(false);
  const [showTopic, setShowTopic] = React.useState(false);
  const [selectedItem, setSelectedItem] = React.useState<AllResourceResponse>();
  const { insertLogDetails } = UseLogClass();

  const isImageOrPdfUrl = (url: string, item: AllResourceResponse): string => {
    if (item.file_extension === "pdf") {
      setResourceType("pdf");
      return "pdf";
    } else if (
      item.file_extension === "jpg" ||
      item.file_extension === "jpeg" ||
      item.file_extension === "png" ||
      item.file_extension === "gif" ||
      item.file_extension === "bmp"
    ) {
      setResourceType("image");
      return "image";
    } else if (
      item.file_extension === "ppt" ||
      item.file_extension === "pptx" ||
      item.file_extension === "doc" ||
      item.file_extension === "docx" ||
      item.file_extension === "xls" ||
      item.file_extension === "xlsx"
    ) {
      setResourceType("document");
      return "document";
    } else if (item.file_extension === "mp4" || item.file_extension === "wmv" || item.file_extension === ".mp4") {
      setResourceType("video");
      return "video";
    } else {
      setResourceType("image");
      return "image";
    }
  };

  const openFileViewer = async (data: AllResourceResponse) => {
    localStorage.removeItem(KEYS.PDF_URL);
    localStorage.removeItem(KEYS.PDF_URL);
    if (isGeneralResource) {
      setShowTopic(false);
      const fileTypeCheck = isImageOrPdfUrl(data.external_url, data);
      if (fileTypeCheck === "document") {
        setViewOpenPPT(true);
        setUrl(data.external_url);
        setPageCount(data.page_count as number);
      }
      if (fileTypeCheck === "video") {
        setViewOpen(true);
        setUrl(data.external_url);
      }
      if (fileTypeCheck === "pdf") {
        localStorage.setItem(KEYS.PDF_URL, data.external_url);
        localStorage.setItem(KEYS.PDF_NAME, data.resource_name);
        router.push(
          `/pages/pdf-viewer?section_id=${null}&page_count=${
            data.page_count
          }&course_id=${data?.course_id}&progress=${
            data?.progress
          }&isGeneral=${isGeneralResource}`
        );
      }
    } else {
      localStorage.removeItem(KEYS.PDF_URL);
      localStorage.removeItem(KEYS.PDF_URL);
      setShowTopic(true);
      const response = await viewResourcePage(
        data?.resource_type,
        data?.resource_id,
        data?.course_module_id
      );

      const resultData = response as ViewResourcePageType;
      const totalPages = resultData.page_count;
      localStorage.setItem(KEYS.RESOURCE_DATA, JSON.stringify(resultData));
      if (resultData?.module_source === "File" || resultData?.module_source === "Document") {
        const fileTypeCheck = isImageOrPdfUrl(resultData?.url, data);
        console.log("fileTypeCheck", fileTypeCheck);

        if (fileTypeCheck === "image") {
          if (data.progress < 100) {
            router.push(
              `/pages/image-viewer?course_id=${data?.course_id}&progress=${data?.progress}`
            );
          } else {
            setViewOpenImage(true);
            setUrl(data.resource_url);
          }
        }

        if (fileTypeCheck === "document") {
          if (data.progress < 100) {
            router.push(
              `/pages/document-viewer?section_id=${null}&page_count=${totalPages}&course_id=${
                data?.course_id
              }&progress=${data?.progress}`
            );
          } else {
            setPageCount(data.page_count as number);
            setViewOpenPPT(true);
            setUrl(data.resource_url);
          }
        }
        if (fileTypeCheck === "pdf") {
          router.push(
            `/pages/pdf-viewer?section_id=${null}&page_count=${totalPages}&course_id=${
              data?.course_id
            }&progress=${data?.progress}&isGeneral=${isGeneralResource}`
          );
        }
      } else if (resultData?.module_source === "Video") {
        if (data.progress < 100) {
          router.push(`/pages/video-player?course_id=${data?.course_id}`);
        } else {
          setViewOpen(true);
          setUrl(data.resource_url);
        }
      } else if (data.resource_type == "Page") {
        router.push(
          `/pages/html-viewer?course_id=${data?.course_id}&progress=${data?.progress}`
        );
      }
    }
  };
  const closeDialog = (): void => {
    setViewOpen(false);
    setViewOpenPPT(false);
    setOpenSkipModal(false);
    setViewOpenImage(false);
  };
  const resourceTemplate = (item: AllResourceResponse) => {
    const index = data.indexOf(item);
    const getIcon = (extension: string) => {
      if (item.resource_type === "File" || item.resource_type === "file") {
        if (extension === "ppt" || extension === "pptx") {
          return "/assets/ppticon.png";
        } else if (extension === "jpg" || extension === "jpeg") {
          return "/assets/imageicon.png";
        }
        return "/assets/imageicon.png";
      } else if (item.resource_type === "URL" || item.resource_type === "url") {
        return "/assets/playicon.png";
      }
    };

    const showAlertIcon =
      process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
        ? item.progress !== 100 && item.resource_status === 0
        : item.progress !== 100;

    // const isLocked = index === 0
    // ? item.progress >= 100 // For the first item, disable only if progress is 100
    // : data.slice(0, index) // For other items, check previous items
    //     .some((prevItem) => prevItem.progress < 100 || prevItem.progress === null) ||
    //   item.progress >= 100;

    const isLocked =
      process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
        ? data
            .slice(0, index)
            .some(
              (prevItem) =>
                (prevItem.progress < 100 || prevItem.progress === null) &&
                prevItem.resource_status !== 1
            )
        : data
            .slice(0, index)
            .some(
              (prevItem) =>
                prevItem.progress < 100 || prevItem.progress === null
            );

    return (
      <div
        className={`relative border border-gray rounded-xl bg-[#FFFF] ${
          isGeneralResource ? "p-2" : "p-4"
        } flex flex-col items-center mx-2 cursor-pointer ${
          isLocked ? "opacity-50 cursor-not-allowed pointer-events-none " : ""
        }`}
        onClick={() => !isLocked && openFileViewer(item)}
      >
        <div
          className={`w-full flex items-center justify-center overflow-hidden rounded-lg ${
            isGeneralResource ? "mb-0" : "mb-3"
          }`}
        >
          <img
            src={
              item.thumbnail_url ? item.thumbnail_url : "/assets/no-image.jpg"
            }
            className={`object-cover ${
              isGeneralResource ? "h-40" : "h-40"
            } w-auto`}
            alt="Thumbnail"
          />
        </div>
        {!isGeneralResource && (
          <h1
            className={`text-sm font-normal text-orange-500 text-center ${
              isGeneralResource ? "mb-1 w-38 h-8" : "mb-1 w-64 h-16"
            }`}
          >
            {t("Section")} : {item.section_name}
          </h1>
        )}

        <div className="w-full h-[1px] bg-violet-500 my-3"></div>

        {item && (
          <span
            className={`mt-2 text-primary text-sm px-2 py-1 rounded-full ${
              isGeneralResource ? "text-xs" : ""
            }`}
          >
            {item.file_extension === "mp4" || item.file_extension === ".mp4"? <VideoIcon /> : <FileIcon />}
          </span>
        )}
        <h4
          className={`text-lg font-semibold text-black text-center ${
            isGeneralResource ? "mb-1 w-48 h-12" : "mb-1 w-64 h-16"
          }`}
        >
          {item.resource_name}
        </h4>

        {item.is_checkpoint_enabled && (
          <div className="absolute top-2 right-2 bg-yellow-400 text-white text-xs px-2 py-1 rounded-full ">
            {t("Checkpoint")}
          </div>
        )}
        {showAlertIcon && (
          <div className="relative">
            <div className="absolute top-2 left-2 text-red-500 group">
              <AlertCircle size={20} />
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-max px-3 py-1 text-sm font-semibold text-white bg-red-500 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                {t("pptCompletionAlert")}
              </div>
            </div>
          </div>
        )}
        {!isGeneralResource && item.progress !== undefined && (
          <div className="w-full mt-3 mb-4">
            <div className="flex justify-between mb-2 text-sm text-gray-600">
              <span> {t("Progress")}</span>
              <span>
                {item.progress !== null && item.progress !== undefined
                  ? `${item.progress >= 100 ? 100 : item.progress.toFixed(2)}%`
                  : "0%"}
              </span>
            </div>
            <div className="relative w-full bg-gray-200 rounded-full h-2.5">
              <div
                className={`h-2 rounded-full ${
                  item.progress === 0 || item.progress == null
                    ? "bg-slate-300"
                    : item.progress >= 100
                    ? "bg-green-500"
                    : "bg-orange-500"
                }`}
                style={{ width: `${item.progress}%` }}
              ></div>
            </div>
          </div>
        )}

        {item.is_part_of_plan ? (
          <div className="h-7 flex items-center justify-center">
            {item.valid_to != null && (
              <Badge
                value={
                  "Exp by: " +
                  moment.utc(item.valid_to).local().format(DATE_FORMAT)
                }
                severity="danger"
                className="border shadow-none rounded-3xl h-7 bg-[#cc0000] border-[#cc0000] text-white"
              />
            )}
          </div>
        ) : (
          <div className="h-7 flex items-center justify-center"></div>
        )}
        {item.progress < 100 &&
        process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true" ? (
          <div className="flex justify-center items-center mt-4 w-full">
            {item.resource_status === 0 ? (
              <>
                <span title={t("Skip")}>
                  <IterationCw
                    className="w-8 h-8 text-primary cursor-pointer"
                    style={{
                      background: "none",
                      border: "none",
                      boxShadow: "none",
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedItem(item);
                      setOpenSkipModal(true);
                    }}
                  />
                </span>
              </>
            ) : (
              <div className="px-3 py-1 bg-gray-200 text-gray-700 text-sm italic rounded-full ">
                {t("Skipped")}
              </div>
            )}
          </div>
        ) : (
          <div className="px-3 py-1 mt-10 ">{""}</div>
        )}
      </div>
    );
  };

  const getMaxWidth = () => {
    if (data.length === 0) return "max-w-[0px]";
    if (data.length === 1) return "max-w-[400px]";
    if (data.length === 2) return "max-w-[800px]";
    if (data.length === 3) return "max-w-[1200px]";
    if (data.length === 4) return "max-w-[1600px]";
    return "max-w-[2000px]";
  };
  const skipResources = async (item: AllResourceResponse) => {
    const orgID = localStorage.getItem(KEYS.ORG_ID);
    const userId = localStorage.getItem("userId");
    let reqParams: SkipVideoRequest = {
      org_id: orgID as string,
      course_id: item.course_id,
      resource_id: item.resource_id,
      user_id: userId as string,
      type: item.resource_type,
      action: "skipped",
      progress: item.progress ? item.progress : 0,
      course_module_id: item.course_module_id,
    };
    try {
      const response = await skipResource(reqParams);
      setOpenSkipModal(false);
      if (response) {
        const updatedItem = { ...item, resource_status: 1 };
        const updatedData = data.map((resource) =>
          resource.resource_id === item.resource_id ? updatedItem : resource
        );
        setData(updatedData);
        // toast({
        //   variant: "success",
        //   title: "Success",
        //   description: SUCCESS_MESSAGES.skip_resource,
        // });
        toast({
          variant: "default",

          title: t("success"),

          description: t("skip_resource"),
        });
        insertLogDetails(
          "Course_Resource",
          "Skip Video",
          `Video Skipped     `,
          "SUCCESS",
          selectedItem?.resource_id as string
        );
      }
    } catch (error) {
      setOpenSkipModal(false);
      toast({
        variant: "destructive",
        title: t("error"),

        description: t("skip_resource"),
      });
      insertLogDetails(
        "Course_Resource",
        "Skip Video",
        `Faied to skip video   `,
        "ERROR",
        selectedItem?.resource_id as string
      );
    }
  };
  return (
    <div>
      {data?.length > 0 ? (
        isModal ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4 ">
            {data.map((item) => (
              <div
                key={item.resource_id}
                className={`relative border border-gray rounded-lg p-2 flex flex-col items-center cursor-pointer ${
                  process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
                    ? data
                        .slice(0, data.indexOf(item))
                        .some(
                          (prevItem) =>
                            (prevItem.progress < 100 ||
                              prevItem.progress === null) &&
                            prevItem.resource_status !== 1
                        )
                      ? "opacity-50 cursor-not-allowed pointer-events-none"
                      : ""
                    : data
                        .slice(0, data.indexOf(item))
                        .some(
                          (prevItem) =>
                            prevItem.progress < 100 ||
                            prevItem.progress === null
                        )
                    ? "opacity-50 cursor-not-allowed pointer-events-none"
                    : ""
                }`}
                onClick={() => openFileViewer(item)}
              >
                <div className="w-full flex items-center justify-center overflow-hidden rounded-lg mb-2 ">
                  <img
                    src={
                      item.thumbnail_url
                        ? item.thumbnail_url
                        : "/assets/no-image.jpg"
                    }
                    className="object-cover h-24 w-auto"
                    alt="Thumbnail"
                  />
                </div>
                {!isGeneralResource && (
                  <h1 className="text-xs font-normal text-orange-500 text-center mb-1 w-full truncate">
                    {t("Section")} : {item.section_name}
                  </h1>
                )}

                <h4 className="text-sm font-semibold text-black text-center mb-1 w-full truncate">
                  {item.resource_name}
                </h4>
                {item.progress !== undefined && (
                  <div className="w-full mt-2">
                    <div className="relative w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          item.progress === 0 || item.progress == null
                            ? "bg-slate-300"
                            : item.progress >= 100
                            ? "bg-green-500"
                            : "bg-orange-500"
                        }`}
                        style={{ width: `${item.progress}%` }}
                      ></div>
                    </div>
                  </div>
                )}
                {item.progress < 100 &&
                process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true" ? (
                  <div className="flex justify-center items-center mt-4 w-full">
                    {item.resource_status === 0 ? (
                      <>
                        <span title="Skip">
                          <IterationCw
                            className="w-8 h-8 text-[#9FC089] cursor-pointer"
                            style={{
                              background: "none",
                              border: "none",
                              boxShadow: "none",
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedItem(item);
                              setOpenSkipModal(true);
                            }}
                          />
                        </span>
                      </>
                    ) : (
                      <div className="px-3 py-1 bg-gray-200 text-gray-700 text-sm italic rounded-full ">
                        {t("Skipped")}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="px-3 py-1 mt-10 ">{""}</div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className={`p-4 rounded-2xl ${getMaxWidth()}`}>
            <Carousel
              value={data}
              numVisible={5}
              numScroll={2}
              itemTemplate={resourceTemplate}
              className="w-full"
              showNavigators={showIndicators}
              responsiveOptions={mobResponsive}
            />
          </div>
        )
      ) : (
        <p className="text-left text-lg text-gray-500 pt-5">
          {t("noTitleFound", { title })}
        </p>
      )}
      {viewOpen && (
        <Modal
          title=""
          header=""
          openDialog={viewOpen}
          closeDialog={closeDialog}
          type="max-w-5xl"
        >
          <VideoDialog onCancel={closeDialog} url={url} showTopic={showTopic} />
        </Modal>
      )}
      {viewOpenPPT && (
        <Modal
          title=""
          header=""
          openDialog={viewOpenPPT}
          closeDialog={closeDialog}
          type="max-w-5xl"
        >
          <DocumentDialog
            onCancel={closeDialog}
            url={url}
            pageCount={pageCount}
            showTopic={showTopic}
          />
        </Modal>
      )}
      {viewOpenImage && (
        <Modal
          title=""
          header=""
          openDialog={viewOpenImage}
          closeDialog={closeDialog}
          type="max-w-5xl"
        >
          <ImageDialog onCancel={closeDialog} url={url} showTopic={showTopic} />
        </Modal>
      )}
      {openSkipModal && (
        <Modal
          title=""
          header=""
          openDialog={openSkipModal}
          closeDialog={closeDialog}
          type="max-w-lg"
        >
          <SkipModal
            closeDialog={closeDialog}
            proceedSkip={() => selectedItem && skipResources(selectedItem)}
          ></SkipModal>
        </Modal>
      )}
    </div>
  );
}
