"use client";
import React, { useEffect, useState } from "react";
import MainLayout from "../layouts/mainLayout";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { Chart } from "chart.js";
import {
  UserStatistics,
  CourseData,
  InnerItem,
  TopicsData,
  ErrorCatch,
  AssignOrgReturn,
  SentEmailRequest,
} from "@/types";
import { getLocalStorageItem } from "@/lib/utils";
import { KEYS } from "@/lib/keys";
import { Spinner } from "@/components/ui/progressiveLoder";
import { useCourse } from "@/hooks/useCourse";
import ViewCourses from "./course-list";
import OverallStatisticsCounts from "./mainDashboradStatistics";
import DashboardAnalytics from "./dashoardAnalytics";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/client";
import useOrganization from "@/hooks/useOrganization";
import { UseTopics } from "@/hooks/useTopics";
import { defaultOrgId, defaultOrgName, defaultRollId } from "@/lib/constants";
import useAuthorization from "@/hooks/useAuth";
import { Modal } from "@/components/ui/modal";
import EditProfile from "@/components/edit-profile";
import CurrentAffairsPreview from "../current-affairs-preview/page";
import { useTheme } from "@/context/ThemeContext";
import { useThemeColors } from "@/hooks/useThemeColors";
import useCustomBranding from "@/hooks/useCustomBranding";

const DashboardLayout = () => {
  const [selectedCourse, setSelectedCourse] = useState<CourseData | null>(null);
  const [courseData, setCourseData] = useState<CourseData[]>([]);
  const [courseId, setCourseId] = useState<string | null>("");
  const [courseLength, setCourseLength] = useState<number>();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isCourseLoading, setCourseIsLoading] = useState<boolean>(true);
  const { getUserAllCourseStatics } = useCourse();
  const [allCourses, setAllCourses] = useState<UserStatistics[]>([]);
  const [isCoursIdSet, setIsCourseIdSet] = useState<boolean>(false);
  const [singleCourseEnrolled, setSingleCourseEnrolled] = useState(true);
  let orgID = getLocalStorageItem(KEYS.ORG_ID);
  let courses = getLocalStorageItem("courseData");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const { getAllProfileData } = useAuthorization();
  const { getUserOrganization, assignOrganization, sentEmail } =
    useOrganization();
  const { getTopicsData } = UseTopics();
  let userId: string;
  const [openProfileUpdate, setOpenProfileUpdate] = useState(false);
  // const {  setTheme } = useTheme();
  const router = useRouter();
  // const [theme, setTheme] = useState('light');
  const savedTheme = getLocalStorageItem("theme");
  const { getCustomBrandingDetails } = useCustomBranding();
  useThemeColors(savedTheme ?? "light");
  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.hash.substring(1));
    const accessToken = queryParams.get("access_token");
    if (accessToken) {
      localStorage.setItem(KEYS.ACCESS_TOKEN, accessToken);
      console.log("Access token set successfully.");

      fetchAuthenticatedUser();
    } else {
      if (!localStorage.getItem(KEYS.ACCESS_TOKEN)) {
        let auth = localStorage.getItem("sb-tsghdsndkborjlbxzfyb-auth-token");
        let parsedAuth = auth ? JSON.parse(auth) : "";
        if (parsedAuth.access_token) {
          localStorage.setItem(KEYS.ACCESS_TOKEN, parsedAuth.access_token);
        }
        let auth_token = parsedAuth.access_token;
        console.log("auth_token", auth_token);
      } else {
        console.log("Access token already exists in localStorage.");
        fetchAuthenticatedUser();
      }
    }
    if (courses) {
      getCourseDatas();
    }
  }, []);

  const fetchAuthenticatedUser = async () => {
    const { data: user, error } = await supabase.auth.getUser();

    if (error) {
      console.error("Error fetching user data:", error.message);
      if (error.status === 403) {
        router.push("/pages/login");
      }
      return;
    }

    const orgResult = await getUserOrganization(user.user.id);
    if (orgResult.length === 0) {
      const domain = user.user.email?.split("@")[1]?.split(".")[0];
      if (domain !== null) {
        console.log("domain validated");
        await sentEmailNotification(user?.user?.email as string);
        console.log("mail fn called");
        try {
          const params = {
            org_id: defaultOrgId,
            role_id: defaultRollId,
            user_ids: [user.user.id as string],
          };
          const response: AssignOrgReturn = await assignOrganization(params);
          console.log("org assign");
          console.log(response);
          try {
            const data = await getCustomBrandingDetails(defaultOrgId as string);
            if (data != null) {
              localStorage.setItem("brandingDetails", JSON.stringify(data));
            } else {
              localStorage.removeItem("brandingDetails");
            }
          } catch (error) {
            console.log("Branding Details:", error);
          }
          fetchTopics(defaultOrgId, defaultOrgName);
        } catch (error) {
          const err = error as ErrorCatch;
        }
        console.log("The domain is citrusinformatics.");
      } else {
        console.log("The domain is not citrusinformatics.");
        router.push("/pages/welcome");
      }
    } else {
      localStorage.setItem(KEYS.ORG_ID, orgResult[0].org_id);
      orgID = getLocalStorageItem(KEYS.ORG_ID);
      localStorage.setItem(KEYS.ORG_NAME, orgResult[0].org_name);
      fetchTopics(orgResult[0].org_id, orgResult[0].org_name);
    }

    console.log("orgResult", orgResult);
    console.log("Authenticated user data:", user);

    localStorage.setItem(KEYS.USER_ID, user.user.id);
    const userDetails = {
      email: user.user.email,
      id: user.user.id,
      last_sign_in_at: user.user.last_sign_in_at,
      user_metadata: {
        email: user.user.email,
        email_verified: user.user.user_metadata.email_verified,
        // first_name: "Security",
        // last_name: "User",
        phone_verified: user.user.user_metadata.phone_verified,
        sub: user.user.user_metadata.sub,
      },
    };
    localStorage.setItem("userDetails", JSON.stringify(userDetails));
    getUserImage(userDetails.id);
  };

  const sentEmailNotification = async (user_email: string): Promise<void> => {
    const params: SentEmailRequest = {
      mail_user: user_email,
      mail_subject: "New Registration",
      mail_content: "",
    };
    console.log("send notification");
    console.log(params);
    try {
      const resp = await sentEmail(params);
      console.log("Notification sent:", resp);
    } catch (error) {
      const err = error as ErrorCatch;
      console.log(err);
    }
  };

  async function getUserImage(data: string): Promise<void> {
    try {
      const profileData = await getAllProfileData(data);
      const image = [{ avatar_url: profileData[0].avatar_url }];
      localStorage.setItem(KEYS.PROFILE_IMAGE, JSON.stringify(image));
      localStorage.setItem("PROFILE_FNAME", profileData[0].first_name ?? "");
      localStorage.setItem("PROFILE_LNAME", profileData[0].last_name ?? "");
      localStorage.setItem(
        "PROFILE_PHONE_NUMBER",
        profileData[0].phonenumber1 ?? ""
      );
      if (
        profileData[0].first_name === null ||
        profileData[0].first_name === undefined
      ) {
        setOpenProfileUpdate(true);
      }
    } catch (error) {
      const err = error as ErrorCatch;
    }
  }

  const handleDialogClose = (): void => {
    setOpenProfileUpdate(false);
  };
  const fetchTopics = async (orgId: string, orgName: string): Promise<void> => {
    const params = {
      org_id: orgId ?? "",
      filter_data: 0,
    };
    try {
      const response: TopicsData[] = await getTopicsData(params);
      console.log("response", response);
      if (response && response.length > 0) {
        if (typeof window !== "undefined") {
          localStorage.setItem(
            "courseData",
            JSON.stringify(
              response.map((item) => item.courses).flat() as CourseData[]
            )
          );
          courses = JSON.stringify(
            response.map((item) => item.courses).flat() as CourseData[]
          );
          getCourseDatas();
          if (response.length === 1) {
            localStorage.setItem(
              KEYS.COURSE_ID,
              response[0].courses?.[0].course_id as string
            );
          }

          localStorage.setItem(KEYS.ORG_NAME, orgName);
        }
      } else {
        router.push("/pages/emptyCourse");
      }
    } catch (error) {
      const err = error as ErrorCatch;
      // toast({
      //   variant: "destructive",
      //   title: "Error",
      //   description: err?.message,
      // });
    }
  };

  const getCourseDatas = async (): Promise<void> => {
    const parsedData = JSON.parse(courses as string);
    setCourseLength(parsedData.length);
    getAllCourseDetails();
    if (parsedData.length === 1) {
      setSingleCourseEnrolled(true);
      setCourseId(parsedData[0].course_id);
      setIsLoading(false);
      setIsCourseIdSet(true);
    } else {
      setSingleCourseEnrolled(false);

      setCourseId(null);
      setIsLoading(false);
      setIsCourseIdSet(true);
    }
    setCourseData(parsedData);
    setSelectedCourse(parsedData[0]);

    setBreadcrumbItems(getBreadCrumbItems("Home", { "": "" }));
  };

  const getAllCourseDetails = async (): Promise<void> => {
    try {
      let user_id = getLocalStorageItem(KEYS.USER_ID) || "";
      const response = await getUserAllCourseStatics({
        org_id: orgID as string,
        user_id: user_id as string,
      });
      setAllCourses(response);

      setCourseIsLoading(false);
    } catch (error) {
      setIsLoading(true);
    }
  };
  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="w-full p-6 pt-0">
        {isCourseLoading ? (
          <div className="flex justify-center items-center h-full">
            <Spinner />
          </div>
        ) : (
          <>
            {process.env.NEXT_PUBLIC_SHOW_CURRENT_AFFAIRS === "true" && (
              <div className="w-full flex-row">
                <CurrentAffairsPreview></CurrentAffairsPreview>
              </div>
            )}

            <div className="w-full flex-row">
              <OverallStatisticsCounts courseId={courseId as string} />
            </div>

            <div className="w-full flex-row">
              <DashboardAnalytics courseID={courseId as string} />
            </div>
            <div className="w-full flex-row">
              <ViewCourses courseData={allCourses} />
            </div>
          </>
        )}
      </div>
      {openProfileUpdate && (
        <Modal
          title="Update Profile"
          header=""
          openDialog={openProfileUpdate}
          closeDialog={handleDialogClose}
          type="max-w-lg"
        >
          <EditProfile closeDialog={handleDialogClose} isDashboard={true} />
        </Modal>
      )}
    </MainLayout>
  );
};

export default DashboardLayout;
