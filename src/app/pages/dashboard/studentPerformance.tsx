import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import { useTranslation } from "next-i18next";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import {
  InfoIcon,
  TrendingUp,
  TrendingDown,
  SortAsc,
  SortDesc,
  Filter,
} from "lucide-react";

interface CourseScore {
  course: string;
  score: number;
  previousScore?: number;
  credits: number;
  attendance: number;
  assignments: number;
}

interface StudentPerformanceProps {
  data?: CourseScore[];
  studentName?: string;
}

const StudentPerformanceChart: React.FC<StudentPerformanceProps> = ({
  data = [
    {
      course: "Mathematics",
      score: 85,
      previousScore: 82,
      credits: 4,
      attendance: 95,
      assignments: 90,
    },
    {
      course: "Physics",
      score: 78,
      previousScore: 75,
      credits: 4,
      attendance: 88,
      assignments: 82,
    },
    {
      course: "Chemistry",
      score: 92,
      previousScore: 88,
      credits: 4,
      attendance: 92,
      assignments: 94,
    },
    {
      course: "Biology",
      score: 88,
      previousScore: 90,
      credits: 3,
      attendance: 85,
      assignments: 89,
    },
    {
      course: "English",
      score: 95,
      previousScore: 92,
      credits: 3,
      attendance: 98,
      assignments: 96,
    },
    {
      course: "History",
      score: 82,
      previousScore: 85,
      credits: 3,
      attendance: 90,
      assignments: 85,
    },
  ],
  studentName = "John Doe",
}) => {
  const { t } = useTranslation("common");
  const [displayData, setDisplayData] = useState(data);
  const [sortField, setSortField] = useState<
    "score" | "credits" | "attendance" | "assignments"
  >("score");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [filterThreshold, setFilterThreshold] = useState<number>(0);
  const [selectedMetric, setSelectedMetric] = useState<
    "score" | "attendance" | "assignments"
  >("score");
  const [animateChart, setAnimateChart] = useState(false);

  // Advanced statistics calculation
  const calculateAdvancedStats = () => {
    const scores = data.map((d) => d.score);
    const n = scores.length;
    const mean = scores.reduce((a, b) => a + b) / n;
    const variance = scores.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / n;
    const stdDev = Math.sqrt(variance);

    const correlationAttendance = calculateCorrelation(
      data.map((d) => d.score),
      data.map((d) => d.attendance)
    );

    const correlationAssignments = calculateCorrelation(
      data.map((d) => d.score),
      data.map((d) => d.assignments)
    );

    return {
      mean: mean.toFixed(2),
      stdDev: stdDev.toFixed(2),
      correlationAttendance: correlationAttendance.toFixed(2),
      correlationAssignments: correlationAssignments.toFixed(2),
      predictedNextScore: (mean + stdDev * 0.5).toFixed(2),
    };
  };

  const calculateCorrelation = (x: number[], y: number[]) => {
    const n = x.length;
    const sumX = x.reduce((a, b) => a + b);
    const sumY = y.reduce((a, b) => a + b);
    const sumXY = x.reduce((a, b, i) => a + b * y[i], 0);
    const sumX2 = x.reduce((a, b) => a + b * b, 0);
    const sumY2 = y.reduce((a, b) => a + b * b, 0);

    return (
      (n * sumXY - sumX * sumY) /
      Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY))
    );
  };

  const advancedStats = calculateAdvancedStats();

  // Sort and filter functions
  useEffect(() => {
    let newData = [...data];

    // Filter
    newData = newData.filter((item) => item[selectedMetric] >= filterThreshold);

    // Sort
    newData.sort((a, b) => {
      const compareResult = b[sortField] - a[sortField];
      return sortDirection === "asc" ? -compareResult : compareResult;
    });

    setDisplayData(newData);
    setAnimateChart(true);
  }, [sortField, sortDirection, filterThreshold, selectedMetric, data]);

  // Reset animation flag after render
  useEffect(() => {
    if (animateChart) {
      const timer = setTimeout(() => setAnimateChart(false), 1000);
      return () => clearTimeout(timer);
    }
  }, [animateChart]);

  // Custom tooltip component (same as before)
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const courseData = data.find((d) => d.course === label);
      return (
        <div className="bg-white p-4 rounded-lg shadow-lg border border-gray-200">
          <p className="font-bold text-gray-900">{label}</p>
          <p className="text-blue-600">
            {" "}
            {t("Score")}: {courseData?.score}%
          </p>
          <p className="text-gray-600">
            {" "}
            {t("Attendance")}: {courseData?.attendance}%
          </p>
          <p className="text-gray-600">
            {" "}
            {t("Assignments")}: {courseData?.assignments}%
          </p>
          <p className="text-gray-600">
            {" "}
            {t("Credits")}: {courseData?.credits}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>
            {" "}
            {t("Course Performance")} - {studentName}
          </CardTitle>
          <Badge variant="secondary" className="text-sm">
            {t("Predicted Next Score")} : {advancedStats.predictedNextScore}%
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Controls */}
          <div className="flex flex-wrap gap-4">
            <Select
              value={sortField}
              onValueChange={(value: any) => setSortField(value)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t("Sort by")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="score"> {t("Score")}</SelectItem>
                <SelectItem value="credits"> {t("Credits")}</SelectItem>
                <SelectItem value="attendance"> {t("Attendance")}</SelectItem>
                <SelectItem value="assignments"> {t("Assignments")}</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              onClick={() =>
                setSortDirection((prev) => (prev === "asc" ? "desc" : "asc"))
              }
              className="flex items-center gap-2"
            >
              {sortDirection === "asc" ? (
                <SortAsc className="h-4 w-4" />
              ) : (
                <SortDesc className="h-4 w-4" />
              )}
              {sortDirection.toUpperCase()}
            </Button>

            <Select
              value={selectedMetric}
              onValueChange={(value: any) => setSelectedMetric(value)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t("Select metric")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="score"> {t("Score")}</SelectItem>
                <SelectItem value="attendance"> {t("Attendance")}</SelectItem>
                <SelectItem value="assignments"> {t("Assignments")}</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={String(filterThreshold)}
              onValueChange={(value) => setFilterThreshold(Number(value))}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t("Filter threshold")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0"> {t("No Filter")}</SelectItem>
                <SelectItem value="70"> {t("Above")}70%</SelectItem>
                <SelectItem value="80"> {t("Above")} 80%</SelectItem>
                <SelectItem value="90"> {t("Above")} 90%</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Alert>
              <InfoIcon className="h-4 w-4" />
              <AlertDescription>
                {t("Mean")} : {advancedStats.mean}%
              </AlertDescription>
            </Alert>
            <Alert>
              <InfoIcon className="h-4 w-4" />
              <AlertDescription>
                {t("Std Dev")} : {advancedStats.stdDev}
              </AlertDescription>
            </Alert>
            <Alert>
              <TrendingUp className="h-4 w-4" />
              <AlertDescription>
                {t("Attendance Correlation")} :{" "}
                {advancedStats.correlationAttendance}
              </AlertDescription>
            </Alert>
            <Alert>
              <TrendingDown className="h-4 w-4" />
              <AlertDescription>
                {t("Assignments Correlation")} :{" "}
                {advancedStats.correlationAssignments}
              </AlertDescription>
            </Alert>
          </div>

          {/* Chart */}
          <div className="h-80 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={displayData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
                <XAxis
                  dataKey="course"
                  angle={-45}
                  textAnchor="end"
                  height={70}
                  tick={{ fill: "#6b7280" }}
                />
                <YAxis domain={[0, 100]} tick={{ fill: "#6b7280" }} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar
                  dataKey={selectedMetric}
                  fill="#3b82f6"
                  radius={[4, 4, 0, 0]}
                  animationBegin={0}
                  animationDuration={animateChart ? 1000 : 0}
                  animationEasing="ease-out"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StudentPerformanceChart;
