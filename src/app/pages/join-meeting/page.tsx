"use client";
import React, { useEffect, useState } from "react";
import MainLayout from "../layouts/mainLayout";
import { Calendar, Video, Users, Link, Key } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  CourseDetailsResultType,
  ErrorCatch,
  InnerItem,
  LiveClassResponse,
  LoginUserData,
  ToastType,
} from "@/types";
import { KEYS } from "@/lib/keys";
import { UseLiveClass } from "@/hooks/useLiveClass";
import moment from "moment";
import { canJoinMeeting, getLocalStorageItem, joinMeeting } from "@/lib/utils";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { useToast } from "@/components/ui/use-toast";
import { UseLogClass } from "@/hooks/useLog";
import { useTranslation } from "next-i18next";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
export default function ZoomController() {
  const [now, setNow] = useState(new Date());
  const [meetingData, setMeetingData] = useState<LiveClassResponse[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<string>("");
  const [courseData, setCourseDetails] = useState<CourseDetailsResultType[]>(
    []
  );
   const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const { getMeetingDetails } = UseLiveClass();
  const { toast } = useToast() as unknown as ToastType;
  const { insertLogDetails } = UseLogClass();
  useEffect(() => {
    getMeetingData();
    // Update current time every 30 seconds
    const interval = setInterval(() => setNow(new Date()), 30000);
    return () => clearInterval(interval);
  }, [selectedCourse]);
  
  useEffect(() => {
    setBreadcrumbItems(getBreadCrumbItems("Join Meeting", {}));
  }, [])

  const getMeetingData = async (): Promise<void> => {
    const orgID = localStorage.getItem(KEYS.ORG_ID);
    let reqParams = {
      org_id: orgID as string,
      course_id: selectedCourse || null,
    };

    try {
      const meetingList = await getMeetingDetails(reqParams);
      setMeetingData(meetingList);
    } catch (error) {
      const err = error as ErrorCatch;
      console.error("Error fetching meetings:", err);
    }
  };

  // Improved logic to determine meeting status
  const getMeetingStatus = (meeting: LiveClassResponse) => {
    const startTime = new Date(meeting.start_date).getTime();
    const endTime = new Date(meeting.end_date).getTime();
    const currentTime = now.getTime();

    if (currentTime >= startTime && currentTime <= endTime) {
      return "live";
    } else if (currentTime < startTime) {
      return "upcoming";
    } else {
      return "ended";
    }
  };

  // Filter and sort meetings: live first, then upcoming, exclude ended
  const filteredAndSortedMeetings =
    meetingData?.length > 0
      ? meetingData
          .filter((meeting) => getMeetingStatus(meeting) !== "ended")
          .sort((a, b) => {
            const statusA = getMeetingStatus(a);
            const statusB = getMeetingStatus(b);

            // Sort live meetings first
            if (statusA === "live" && statusB !== "live") return -1;
            if (statusA !== "live" && statusB === "live") return 1;

            // Then sort by start time for upcoming meetings
            return (
              new Date(a.start_date).getTime() -
              new Date(b.start_date).getTime()
            );
          })
      : [];

  const timeUntilStart = (startTime: string) => {
    const start = new Date(startTime).getTime();
    const nowTime = now.getTime();

    if (nowTime >= start) return null;

    const diffMs = start - nowTime;
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 60) return `${diffMins} min`;

    const hours = Math.floor(diffMins / 60);
    const mins = diffMins % 60;
    return `${hours}h ${mins}m`;
  };

  const getPlatformColor = (platform: string) => {
    return platform === "zoom"
      ? "bg-blue-100 text-blue-800"
      : "bg-green-100 text-green-800";
  };

  const handleJoin = async (meeting: LiveClassResponse) => {
    const USER_DATA = getLocalStorageItem(KEYS.USER_DETAILS);
    const userInfo = JSON.parse(USER_DATA ?? "{}") as LoginUserData;
    await insertLogDetails(
      "Live_Class",
      "Live Class",
      "Joined Meeting ",
      "SUCCESS",
      userInfo.id || ""
    );
    // Use the helper function to join the meeting
    if (!joinMeeting(meeting)) {
      toast({
        variant: "destructive",
        title: t("error"),

        description: t("join_meeting"),
      });
      await insertLogDetails(
        "Live_Class",
        "Live Class",
        "Failed To Joined Meeting ",
        "ERROR",
        meeting.meeting_id || ""
      );
    }
  };

  const handleCopyLink = (url: string) => {
    navigator.clipboard.writeText(url);
    // toast({
    //   variant: "success",
    //   title: SUCCESS_MESSAGES.success,
    //   description: SUCCESS_MESSAGES.link_copied,
    // });
    toast({
      variant: "default",

      title: t("success"),

      description: t("link_copied"),
    });
  };

  const handleCopyId = (id: string) => {
    navigator.clipboard.writeText(id);
    // toast({
    //   variant: "success",
    //   title: SUCCESS_MESSAGES.success,
    //   description: SUCCESS_MESSAGES.id_copied,
    // });
    toast({
      variant: "success",

      title: t("success"),

      description: t("id_copied"),
    });
  };

  const handleCopyPasscode = (passcode: string) => {
    navigator.clipboard.writeText(passcode);

    toast({
      variant: "success",

      title: t("success"),

      description: t("passcode_copied"),
    });
  };

  useEffect(() => {
    const courseDatas = localStorage.getItem("courseData");
    let data = JSON.parse(courseDatas as string);
    if (courseDatas) {
      setCourseDetails(data);
    }
  }, []);

  const handleSelectionChange = (value: string) => {
    setSelectedCourse(value);
  };
  const { t } = useTranslation("common");
  return (
    <MainLayout titleText="">
        <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="mx-auto p-6">
        <div className="flex items-center justify-between mb-6">
          {/* <h1 className="text-2xl font-bold text-gray-800">
            {" "}
            {t("Your Meetings")}
          </h1> */}
        </div>

        <div className="mb-6">
          <Select
            value={selectedCourse || ""}
            onValueChange={handleSelectionChange}
          >
            <SelectTrigger className="w-full max-w-md mx-auto">
              <SelectValue placeholder={t("Select a Course")}>
                {selectedCourse
                  ? courseData.find((c) => c.course_id === selectedCourse)
                      ?.full_name
                  : "Select a Course"}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {courseData.map((course) => (
                <SelectItem
                  key={course.course_id}
                  value={course.course_id}
                  className="hover:bg-gray-100"
                >
                  {course.full_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {filteredAndSortedMeetings.length > 0 ? (
            filteredAndSortedMeetings.map((meeting) => {
              const meetingStatus = getMeetingStatus(meeting);
              const timeLeft =
                meetingStatus === "upcoming"
                  ? timeUntilStart(meeting.start_date)
                  : null;
              const platform =
                meeting.meeting_type || meeting.meeting_type || "zoom";

              return (
                <div
                  key={meeting.meeting_id}
                  className={`border rounded-xl shadow-sm transition overflow-hidden ${
                    meetingStatus === "live"
                      ? "border-green-400 shadow-md"
                      : "border-gray-200"
                  }`}
                >
                  <div className="flex items-center justify-between p-4 border-b border-gray-100">
                    <div className="flex items-center">
                      <div className="h-10 w-10 flex items-center justify-center rounded-full bg-gray-100 text-gray-600">
                        {platform === "zoom" ? (
                          <Video size={20} />
                        ) : (
                          <Users size={20} />
                        )}
                      </div>
                      <div className="ml-3">
                        <span
                          className={`inline-block px-2 py-1 text-xs rounded-full ${getPlatformColor(
                            platform
                          )}`}
                        >
                          {platform === "zoom" ? "Zoom" : "Google Meet"}
                        </span>
                      </div>
                    </div>
                    {meetingStatus === "live" && (
                      <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-medium">
                        {t("Live")}
                      </span>
                    )}
                    {meetingStatus === "upcoming" && timeLeft && (
                      <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-xs font-medium">
                        {t("In")} {timeLeft}
                      </span>
                    )}
                  </div>

                  <div className="p-4 space-y-3">
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar size={16} className="mr-2" />
                      <span>
                        {moment
                          .utc(meeting.start_date)
                          .local()
                          .format("DD/MM/YYYY hh:mm A")}{" "}
                        -{" "}
                        {moment
                          .utc(meeting.end_date)
                          .local()
                          .format("DD/MM/YYYY hh:mm A")}
                      </span>
                    </div>

                    <div className="flex flex-wrap gap-2 text-sm">
                      {meeting.meeting_id && (
                        <button
                          onClick={() => handleCopyId(meeting.meeting_id || "")}
                          className="flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-gray-700"
                        >
                          <Users size={14} className="mr-1" />
                          {t("ID")} : {meeting.meeting_id}
                        </button>
                      )}

                      {meeting.passcode && (
                        <button
                          onClick={() => handleCopyPasscode(meeting.passcode)}
                          className="flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-gray-700"
                        >
                          <Key size={14} className="mr-1" />
                          {t("Passcode")} : {meeting.passcode}
                        </button>
                      )}

                      {meeting.meeting_url && (
                        <button
                          onClick={() =>
                            handleCopyLink(meeting.meeting_url || "")
                          }
                          className="flex items-center px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-gray-700"
                        >
                          <Link size={14} className="mr-1" />
                          {t("Copy Link")}
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="p-4 pt-0">
                    {meetingStatus === "live" ? (
                      <button
                        onClick={() => handleJoin(meeting)}
                        className="w-full py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center justify-center font-medium transition"
                        disabled={!canJoinMeeting(meeting)}
                      >
                        <Video size={18} className="mr-2" />
                        {t("Join Meeting")}
                      </button>
                    ) : (
                      <button
                        disabled
                        className="w-full py-2 bg-gray-200 text-gray-500 rounded-lg flex items-center justify-center font-medium cursor-not-allowed"
                      >
                        {t("Not Started Yet")}
                      </button>
                    )}
                  </div>
                </div>
              );
            })
          ) : (
            <div className="col-span-2 bg-gray-50 rounded-xl p-8 text-center">
              <div className="mx-auto w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
                <Calendar size={24} className="text-gray-500" />
              </div>
              <h3 className="text-lg font-medium text-gray-700 mb-2">
                {t("No Meetings Found")}
              </h3>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
