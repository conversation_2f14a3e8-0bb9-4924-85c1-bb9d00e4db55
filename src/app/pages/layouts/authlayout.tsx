"use client";

import React, { useEffect, useState, type <PERSON>ps<PERSON>ithChildren } from "react";
import Image from "next/image";
import { CustomBrandingDetails } from "@/types";

const AuthLayout = ({ children }: PropsWithChildren): React.JSX.Element => {
  const [customBranding, setCustomBranding] = useState<CustomBrandingDetails>()
  useEffect(() => {
    const brandingDetails = localStorage.getItem("brandingDetails");
   if (brandingDetails) {
       try {
         const theme_name = localStorage.getItem('theme')
                 const parsedBranding:  CustomBrandingDetails[] = JSON.parse(brandingDetails);
                 const brand = parsedBranding?.find(b => b.theme_name === theme_name);
                 setCustomBranding(brand);
       
      } catch (error) {
        console.error("Failed to parse branding details from localStorage", error);
      }
    }
  }, []);

  return ( 
    <div className="min-h-screen flex bg-[var(--color-background)] font-custom " >
      {/* Left half: image */}
      <div className="w-1/2 relative hidden lg:block">
        <Image
          src={customBranding?customBranding.banner_image  as string : "/assets/login-bg.jpg"}
          alt="SmartLearn Logo"
          layout="fill"
          objectFit="cover"
        />
        <div className="absolute top-0 left-0 m-4 z-10">
          <Image
            src={customBranding?customBranding.main_logo  as string : "/images/smartlearn.png"}
            alt="Card Image"
            width={100}
            height={100}
          />
        </div>

      </div>
      <div className="w-full lg:w-1/2 flex items-center justify-center p-4">
        <span>
          <div className="m-4 z-10 flex justify-center">
            <Image
              src={customBranding?customBranding.main_logo  as string : "/images/smartlearn.png"}
              alt="Card Image"
              width={150}
              height={150}
            />
          </div>
          {children}
        </span>
      </div>
    </div>
  );
};

export default AuthLayout;
