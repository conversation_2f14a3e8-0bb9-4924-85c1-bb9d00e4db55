import React, { useEffect, useState } from "react";
import { ExamReviewType, ReviewAnswersType } from "@/types";
import { FaCheckCircle, FaTimesCircle } from "react-icons/fa";
import { Button } from "@/components/ui/button";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { useTranslation } from "next-i18next";
export default function AnswerModal({
  onCancel,
  data,
  index,
}: {
  index: number;
  onCancel: () => void;
  isModal?: boolean;
  data: ExamReviewType[];
}): React.JSX.Element {
  const [question, setQuestion] = useState<string>("");
  const [answers, setAnswers] = useState<ReviewAnswersType[]>([]);
  const [selectedAnswerIds, setSelectedIds] = useState<string[]>([]);
  const [correctAnswer, setCorrectAnswer] = useState<string | null>(null);
  const { t } = useTranslation("common");
  useEffect(() => {
    if (data !== undefined) {
      const questionData = data[0].quest_answers;
      if (questionData) {
        setQuestion(questionData[index].question_text);
        setAnswers(questionData[index].answers);
        setSelectedIds(questionData[index].selected_answer_ids);
        const correctAns = questionData[index].answers.find(
          (ans) => ans.is_correct_answer === true
        );
        setCorrectAnswer(correctAns ? correctAns.answer : null);
      }
    }
  }, [data, index]);

  const getSelectedAnswerText = (answerId: string) => {
    const answer = answers.find((ans) => ans.answer_id === answerId);
    return answer ? answer.answer : "Answer text not available";
  };

  const isAnswerCorrect = (answerId: string) => {
    const answer = answers.find((ans) => ans.answer_id === answerId);
    return answer ? answer.is_correct_answer === true : false;
  };

  const allAnswersCorrect = selectedAnswerIds.every((id) =>
    isAnswerCorrect(id)
  );

  const closeModal = (): void => {
    onCancel();
  };
  return (
    <>
      <div
        className="border common-blue-border rounded p-4 pt-0 answer-border"
        id="answer-modal"
      >
        <div className="pt-5">
          <p className="text-center">
            {" "}
            {t("Question No")}: {index + 1}
          </p>
          <div
            dangerouslySetInnerHTML={{ __html: question }}
            className="pt-2"
          />
          {selectedAnswerIds.length > 0 && (
            <div className="flex items-center mt-2">
              {allAnswersCorrect ? (
                <>
                  <FaCheckCircle className="text-green-500 mr-2" />
                  <span className="text-green-500">{t("correctAnswer")}</span>
                </>
              ) : (
                <>
                  <FaTimesCircle className="text-red-500 mr-2" />
                  <span className="text-red-500">{t("wrongAnswer")}</span>
                </>
              )}
            </div>
          )}
          {selectedAnswerIds.length > 0 && <hr className="my-4" />}
          {selectedAnswerIds.length > 0 ? (
            <div>
              <p className="skipped-mark-color mt-2"> {t("Your Answer")}:</p>
              <div className="mt-2">
                {selectedAnswerIds.map((answerId, idx) => (
                  <div key={idx}>
                    <span
                      dangerouslySetInnerHTML={{
                        __html: getSelectedAnswerText(answerId),
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div>
              <p className="skipped-mark-color mt-2">{t("skippedText")}</p>
              <hr className="my-4" />
            </div>
          )}
          {correctAnswer &&
            (selectedAnswerIds.length === 0 || !allAnswersCorrect) && (
              <div>
                <p className="skipped-mark-color mt-2">
                  {t("Correct Answer")}:
                </p>
                <div
                  className="mt-2"
                  dangerouslySetInnerHTML={{ __html: correctAnswer }}
                />
              </div>
            )}
        </div>
        <div className="flex justify-center mt-4">
          <Button
            onClick={closeModal}
            className="btn-bg-green text-white rounded "
          >
            {t("OK")}
          </Button>
        </div>
      </div>
    </>
  );
}
