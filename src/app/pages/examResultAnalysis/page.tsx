"use client";
import React, { useState, useEffect } from "react";
import MainLayout from "../layouts/mainLayout";
import { But<PERSON> } from "@/components/ui/button";
import StatusIcon from "./statusIcon";
import { Separator } from "@/components/ui/separator";
import { useTranslation } from "next-i18next";
import {
  BookCheck,
  CircleX,
  FileSearch2,
  FileUp,
  Gauge,
  Key,
} from "lucide-react";
import { Chart } from "primereact/chart";
import "primereact/resources/primereact.css";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import { useRouter, useSearchParams } from "next/navigation";
import { useExam } from "@/hooks/useExam";
import {
  AttemptedQuizType,
  ExamReviewType,
  InnerItem,
  LoginUserData,
  UpdateMeetingDetailsRequest,
} from "@/types";
import { reattendText } from "@/lib/constants";
import {
  Accordion,
  AccordionContent,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { AccordionItem } from "@radix-ui/react-accordion";
import { Spinner } from "@/components/ui/progressiveLoder";
import { KEYS } from "@/lib/keys";
import "../../../styles/main.css";
import { Modal } from "@/components/ui/modal";
import AnswerModal from "./answerModal";
import { ActiveElement, ChartEvent } from "chart.js";
import { getLocalStorageItem } from "@/lib/utils";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { UseLogClass } from "@/hooks/useLog";

export default function ExamResultAnalysis(): React.JSX.Element {
  const { t } = useTranslation("common");
  const [doughnutData, setDoughnutData] = useState({});
  const [scoredMark, setScoredMark] = useState<number>();
  const [totalMark, setTotalMark] = useState<number>();
  const [doughnutOptions, setDoughnutOptions] = useState({});
  const [lineData, setLineData] = useState({});
  const [lineOptions, setLineOptions] = useState({});
  const searchParams = useSearchParams();
  const courseId = searchParams?.get("course_id") as string;
  const quizId = searchParams?.get("quiz_id") as string;
  const quizAttemptId = searchParams?.get("quiz_attempt_id") as string;
  const isCheckpointExam = searchParams?.get("checkpoint_exam") as string;
  const tab = searchParams?.get("tab") as string;
  const [examReview, setExamReview] = useState<ExamReviewType[]>([]);
  const [status, setStatus] = useState<string>("");
  const { examResultAnalysis } = useExam();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [index, setIndex] = useState<number>();
  const [isOpen, setIsOpenModal] = useState<boolean>(false);
  const router = useRouter();
  const orgID = localStorage.getItem(KEYS.ORG_ID);
  const USER_DATA = getLocalStorageItem(KEYS.USER_DETAILS);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const { insertLogDetails } = UseLogClass();
  const [examName, setExamName] = useState("");
  useEffect(() => {
    evaluateAnswers();
    if (tab) {
      setBreadcrumbItems(
        getBreadCrumbItems("Result Analysis View", {
          quiz_id: quizId,
          quiz_attempt_id: quizAttemptId,
          course_id: courseId,
          checkpoint_exam: isCheckpointExam,
        })
      );
    } else {
      setBreadcrumbItems(
        getBreadCrumbItems("Result Analysis", {
          quiz_id: quizId,
          quiz_attempt_id: quizAttemptId,
          course_id: courseId,
          checkpoint_exam: isCheckpointExam,
        })
      );
    }
  }, []);
  const evaluateAnswers = async (): Promise<void> => {
    const answerDetails = localStorage.getItem(KEYS.ANSWER_EVALUATION);
    if (answerDetails !== null && answerDetails !== undefined) {
      const questionsData = JSON.parse(answerDetails) as ExamReviewType[];
      analyseResult();
      setExamName(questionsData[0].name);
      const ansEvaluation = questionsData as ExamReviewType[];
      setExamReview(ansEvaluation);
      const scoredMark: number =
        typeof ansEvaluation[0].scored_mark === "string"
          ? parseInt(ansEvaluation[0].scored_mark)
          : ansEvaluation[0].scored_mark;
      if (scoredMark < ansEvaluation[0].pass_mark) {
        setStatus("Failed");
      } else {
        setStatus("Passed");
      }
      let correct_answer = ansEvaluation[0]?.correct_answer_count;
      let wrong_answer = ansEvaluation[0]?.wrong_answer_count;
      let skipped_answer = ansEvaluation[0]?.skipped_ans_count;
      setScoredMark(Number(Number(ansEvaluation[0]?.scored_mark).toFixed(2)));
      setTotalMark(ansEvaluation[0]?.total_mark);
      const openAnswerModal = (
        event: ChartEvent,
        elements: ActiveElement[]
      ): void => {
        if (elements != undefined) {
          setIndex(elements[0].index);
          setIsOpenModal(!isOpen);
        }
      };

      const datas = {
        // labels: ["Correct", "Wrong", "Skipped"],
        labels: [t("Correct"), t("Wrong"), t("Skipped")],
        datasets: [
          {
            data: [correct_answer, wrong_answer, skipped_answer],
            backgroundColor: ["#00A642", "#FF2B2B", "#FFC107"],
            hoverBackgroundColor: ["#00A642", "#FF2B2B", "#FFC107"],
          },
        ],
      };
      const option = {
        cutout: "60%",
      };

      setDoughnutData(datas);
      setDoughnutOptions(option);
      const labels = ansEvaluation[0]?.quest_answers?.map(
        (qa, index) => `Q${index + 1}`
      );
      const graph_data = ansEvaluation[0]?.quest_answers?.map((qa) => qa.mark);
      const documentStyle = getComputedStyle(document.documentElement);
      const textColor = documentStyle.getPropertyValue("--text-color");
      const textColorSecondary = documentStyle.getPropertyValue(
        "--text-color-secondary"
      );
      const surfaceBorder = documentStyle.getPropertyValue("--surface-border");
      const data = {
        labels: labels,
        datasets: [
          {
            label: "",
            data: graph_data,
            fill: false,
            borderColor: documentStyle.getPropertyValue("--black-500"),
            tension: 0.4,
          },
        ],
      };
      const options = {
        maintainAspectRatio: false,
        aspectRatio: 0.6,
        plugins: {
          legend: {
            labels: {
              color: textColor,
            },
          },
        },
        scales: {
          x: {
            ticks: {
              color: textColorSecondary,
            },
            grid: {
              color: surfaceBorder,
            },
          },
          y: {
            ticks: {
              color: textColorSecondary,
            },
            grid: {
              color: surfaceBorder,
            },
          },
        },
        onClick: (event: ChartEvent, elements: ActiveElement[]) =>
          openAnswerModal(event, elements),
      };

      setLineData(data);
      setLineOptions(options);
      analyseResult();
    }
  };
  const analyseResult = async (): Promise<void> => {
    if (USER_DATA !== null && USER_DATA !== undefined) {
      const userInfo = JSON.parse(USER_DATA) as LoginUserData;

      const passData = {
        user_id: userInfo.id as string,
        org_id: orgID as string,
        course_id: courseId as string,
        type_of_quiz: "Practice",
      };

      const fetchData = async (): Promise<void> => {
        try {
          await examResultAnalysis(passData as AttemptedQuizType);
          setIsLoading(false);
        } catch (error: unknown) {
          setIsLoading(false);
        }
      };
      fetchData().catch((error) => console.log(error));
    }
  };
  const goBackToList = (): void => {
    if (tab) {
      router.push(`/pages/exams-list?course_id=${courseId}&tab=${tab}`);
    } else {
      router.push(`/pages/exams-list?course_id=${courseId}`);
    }

    localStorage.removeItem(KEYS.ANSWER_EVALUATION);
  };

  const closeModal = (): void => {
    setIsOpenModal(false);
  };

  const viewSolutions = async (): Promise<void> => {
    router.push(`/pages/view-results?quiz_id=${quizId}&course_id=${courseId}`);
    await insertLogDetails(
      "Exam",
      "Exam  Result Analysis",
      "Analysed exam result ",
      "SUCCESS",
      quizId
    );
  };
  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      {isLoading ? (
        <Spinner />
      ) : (
        <div>
          <div className="flex justify-end mb-4">
            {tab === null && (
              <Button
                variant="outline"
                className="text-[#fb8500] border-[#fb8500] rounded-full hover:bg-transparent hover:text-[#fb8500]"
                onClick={goBackToList}
              >
                {t("Finish")}
              </Button>
            )}
          </div>
          <div className="flex flex-col items-center">
            <StatusIcon status={status} />
            {status === "Failed" && (
              <div className="text-center mt-4 text-[var(--color-font-color)]">
                <p>{t("reattendText")}</p>
              </div>
            )}
            <div className="text-center mt-4 mb-4">
              <div className="flex justify-center space-x-4 mb-4 text-[var(--color-font-color)]">
                <div className="flex items-center">
                  <div className="w-4 h-4 chart-bg-green "></div>
                  <span className="ml-2">
                    {t("Correct")} ({examReview[0]?.correct_answer_count})
                  </span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 chart-bg-red"></div>
                  <span className="ml-2">
                    {t("Wrong")} ({examReview[0]?.wrong_answer_count})
                  </span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 chart-bg-yellow"></div>
                  <span className="ml-2">
                    {t("Skipped")} ({examReview[0]?.skipped_ans_count})
                  </span>
                </div>
              </div>
            </div>
          </div>
          <Separator />

          <div className="flex justify-center mt-4 mb-8">
            <Chart
              type="doughnut"
              data={doughnutData}
              options={doughnutOptions}
              className="mx-auto"
            />
          </div>

          <Separator />

          <div className="mt-4 mb-8">
            <Chart
              type="line"
              data={lineData}
              options={lineOptions}
              className="mx-auto"
            />
            <div></div>
          </div>
          <Separator />
          <div className="text-center mt-4 mb-4">
            <p className="text-lg font-bold"> {t("Score Sheet")}</p>
            {/* <p className="mt-2">
              Here is your score sheet. You can click on the graph and review
              your answers and the score you have obtained.
            </p> */}
            <p className="mt-2">
              {t(
                "Here is your score sheet. You can click on the graph and review"
              )}
              <br></br> {t("your answers and the score you have obtained")}
            </p>
          </div>
          <Separator />

          <div>
            <div className="flex space-x-4 mt-4">
              <div className="w-20 h-20 rounded-2xl border-4 border-orange-100 flex justify-center items-center">
                <Gauge className="text-[#FB8500] w-12 h-12" />
              </div>
              <div className="mt-2">
                <p className="text-lg font-bold">{t("Mark")}</p>
                <p className="text-2xl text-[#FB8500]">
                  {scoredMark ?? 0}/{Math.floor(totalMark ?? 0)}
                </p>
              </div>
            </div>

            <div className="col-span-1 flex justify-center mt-4">
              <div className="mark-card-bg rounded-lg w-full h-full flex flex-col justify-center items-center">
                <div className="mt-4 flex items-center ml-0">
                  <p className="text-white font-bold text-xl">
                    {t("Total Marks Scored")}
                  </p>
                </div>
                <div className="bg-white rounded-md w-11/12 h-96 mt-4 mb-10 p-4 flex flex-col justify-between">
                  <div className="flex items-center mb-2">
                    <div className="w-16 h-16 rounded-2xl border-4 border-slate-300 flex justify-center items-center">
                      <FileSearch2 className="file-search-color w-12 h-12" />
                    </div>
                    <span className="ml-2 text-lg file-search-color">
                      {t("Attended")}
                    </span>
                    <div className="ml-auto text-lg file-search-color">
                      {examReview[0]?.correct_answer_count +
                        examReview[0]?.wrong_answer_count}
                    </div>
                  </div>
                  <Separator className="border-2 border-slate-300" />
                  <div className="flex items-center my-2">
                    <div className="w-16 h-16 rounded-2xl border-4 border-slate-300 flex justify-center items-center">
                      <BookCheck className="correct-mark-color w-12 h-12" />
                    </div>
                    <span className="ml-2 text-lg correct-mark-color">
                      {t("Correct")}
                    </span>
                    <div className="ml-auto text-lg correct-mark-color">
                      {examReview[0]?.correct_answer_count}
                    </div>
                  </div>
                  <Separator className="border-2 border-slate-300" />
                  <div className="flex items-center my-2">
                    <div className="w-16 h-16 rounded-2xl border-4 border-slate-300 flex justify-center items-center">
                      <CircleX className="wrong-mark-color w-12 h-12" />
                    </div>
                    <span className="ml-2 text-lg wrong-mark-color">
                      {" "}
                      {t("Wrong")}
                    </span>
                    <div className="ml-auto text-lg wrong-mark-color">
                      {examReview[0]?.wrong_answer_count}
                    </div>
                  </div>
                  <Separator className="border-2 border-slate-300" />
                  <div className="flex items-center my-2 mb-2">
                    <div className="w-16 h-16 rounded-2xl border-4 border-slate-300 flex justify-center items-center">
                      <FileUp className="skipped-mark-color w-12 h-12" />
                    </div>
                    <span className="ml-2 text-lg skipped-mark-color">
                      {t("Skipped")}
                    </span>
                    <div className="ml-auto text-lg skipped-mark-color">
                      {examReview[0]?.skipped_ans_count}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <div className="text-xl text-black font-bold text-[var(--color-font-color)]">
              <p> {t("Topic wise Statistics")}</p>
            </div>

            <Accordion type="single" collapsible>
              {examReview[0]?.category_wise_summary?.map((item, index) => {
                const totalAnswers =
                  item.correct_answer_count +
                  item.wrong_answer_count +
                  item.skipped_ans_count;
                const correctPercentage = totalAnswers
                  ? (item.correct_answer_count / totalAnswers) * 100
                  : 0;
                const wrongPercentage = totalAnswers
                  ? (item.wrong_answer_count / totalAnswers) * 100
                  : 0;
                const skippedPercentage = totalAnswers
                  ? (item.skipped_ans_count / totalAnswers) * 100
                  : 0;

                return (
                  <AccordionItem key={index} value={`item-${index + 1}`}>
                    <AccordionTrigger className="bg-white  text-blue font-semibold rounded-lg p-4">
                      {item.question_category_name}
                    </AccordionTrigger>
                    <AccordionContent className="bg-white text-[var(--color-font-color)] p-4 border-2 rounded-b-lg">
                      <div className="w-full mt-4 mb-4">
                        <div className="flex justify-between space-x-4 mb-4">
                          <div className="flex items-center">
                            <div className="w-4 h-4 bg-[#00A642]"></div>
                            <span className="ml-2">
                              {t("Correct")} ({item.correct_answer_count})
                            </span>
                          </div>
                          <div className="flex items-center">
                            <div className="w-4 h-4 bg-[#FF2B2B]"></div>
                            <span className="ml-2">
                              {t("Wrong")} ({item.wrong_answer_count})
                            </span>
                          </div>
                          <div className="flex items-center">
                            <div className="w-4 h-4 bg-[#FFC107]"></div>
                            <span className="ml-2">
                              {t("Skipped")} ({item.skipped_ans_count})
                            </span>
                          </div>
                        </div>
                        <div className="bg-[#000] rounded-2xl h-24 w-full px-4 py-4">
                          <div className="flex justify-between items-center">
                            <p className="text-white text-lg font-bold">
                              {examName}
                            </p>
                            <p className="text-white text-lg font-bold">
                              {item.correct_answer_count}/
                              {item.wrong_answer_count +
                                item.correct_answer_count +
                                item.skipped_ans_count}
                            </p>
                          </div>
                          <div className="flex w-full h-2 mt-4">
                            <div
                              className="h-full"
                              style={{
                                width: `${correctPercentage}%`,
                                backgroundColor: "#00A642",
                              }}
                            ></div>
                            <div
                              className="h-full"
                              style={{
                                width: `${wrongPercentage}%`,
                                backgroundColor: "#FF2B2B",
                              }}
                            ></div>
                            <div
                              className="h-full"
                              style={{
                                width: `${skippedPercentage}%`,
                                backgroundColor: "#FFC107",
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </div>

          <div className="w-full mt-4">
            {tab === null ? (
              <Button
                className="w-full rounded-full text-xl "
                onClick={viewSolutions}
              >
                {t("View Solutions")}
              </Button>
            ) : (
              <div className="flex justify-end mt-4 sticky bottom-2">
                <Button
                  variant="outline"
                  className="rounded-md"
                  onClick={goBackToList}
                >
                  {t("Back")}
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
      {isOpen && (
        <Modal
          title={""}
          header=""
          openDialog={isOpen}
          closeDialog={closeModal}
          type="max-w-xl"
        >
          <AnswerModal
            data={examReview as ExamReviewType[]}
            onCancel={closeModal}
            index={index as number}
          />
        </Modal>
      )}
    </MainLayout>
  );
}
