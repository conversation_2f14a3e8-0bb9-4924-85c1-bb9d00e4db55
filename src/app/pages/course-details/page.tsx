"use client";
import React, { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import MainLayout from "../layouts/mainLayout";
import { useRouter, useSearchParams } from "next/navigation";
import {
  CourseDetailsRequest,
  CourseDetailsResultType,
  ErrorCatch,
  InnerItem,
} from "@/types";
import { KEYS } from "@/lib/keys";
import { useCourse } from "@/hooks/useCourse";
import ResourcesList from "../dashboard/resources";
import { Button } from "@/components/ui/button";
import CourseStatisticsCounts from "../dashboard/courseStatisticsCounts";
import CourseStatisticsGraphs from "../dashboard/courseStatisticsGraphs";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { LayoutDashboard } from "lucide-react";
import { useTranslation } from "next-i18next";
const CourseDisplay = () => {
  const { t } = useTranslation("common");
  const searchParams = useSearchParams();
  const courseId = searchParams?.get("course_id") as string;
  const { courseDetails } = useCourse();
  const [courseData, setCourseDetails] = useState<CourseDetailsResultType>();
  const [categoryId, setCategoryId] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  useEffect(() => {
    fetchCourseRankList();
    setBreadcrumbItems(
      getBreadCrumbItems("Course Details", {
        courseId: courseId as string,
        // categoryId: categoryId as string,
        course_id: courseId,
      })
    );
  }, []);

  const fetchCourseRankList = async (): Promise<void> => {
    const orgID = localStorage.getItem(KEYS.ORG_ID);
    const params: CourseDetailsRequest = {
      org_id: orgID as string,
      course_id: courseId as string,
    };
    try {
      const response = await courseDetails(params);
      setCourseDetails(response[0]);
      setCategoryId(response[0].category_id);
      setIsLoading(false);
    } catch (error) {
      const err = error as ErrorCatch;
    }
  };
  const handleCancel = () => {
    router.push(`/pages/dashboard`);
  };
  return (
    <MainLayout titleText={""}>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div>
        <CardContent className="">
          <div className="">
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold">
                {t("Course Dashboard")} - {courseData?.full_name}
              </h1>
              {/* <span title="View Sessions">   <LayoutDashboard className="text-red-500 cursor-pointer" onClick={() => router.push(`/pages/section-details`)}/></span> */}
            </div>

            <div className="mt-6">
              <CourseStatisticsCounts courseId={courseId as string} />
              {!isLoading && (
                <CourseStatisticsGraphs
                  courseID={courseId as string}
                  categoryID={categoryId}
                />
              )}
            </div>
            <div>
              <ResourcesList courseID={courseId as string} />
            </div>
          </div>
        </CardContent>
      </div>
      <div className="flex justify-end mt-4 sticky bottom-2">
        <Button variant="outline" className="rounded-md" onClick={handleCancel}>
          {t("Back")}
        </Button>
      </div>
    </MainLayout>
  );
};

export default CourseDisplay;
