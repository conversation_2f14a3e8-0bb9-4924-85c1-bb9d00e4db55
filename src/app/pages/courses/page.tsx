"use client";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Star } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import MainLayout from "../layouts/mainLayout";
import { useCourse } from "@/hooks/useCourse";
import { CoursesParams, InnerItem } from "@/types";
import { getLocalStorageItem } from "@/lib/utils";
import { useThemeColors } from "@/hooks/useThemeColors";
import { useTranslation } from "next-i18next";
import { Card, CardContent } from "@/components/ui/card";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { Modal } from "@/components/ui/modal";
import CourseRequestConfirm from "./CourseRequestConfirm";

export default function CoursesPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [courses, setCourses] = useState<CoursesParams[] | undefined>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<CoursesParams | null>(
    null
  );
  const { listCourses } = useCourse();
  const { t } = useTranslation("common");
  const { toast } = useToast();
  const { requestCourse } = useCourse();
  const savedTheme = getLocalStorageItem("theme");
  useThemeColors(savedTheme ?? "light");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  useEffect(() => {
    setBreadcrumbItems(getBreadCrumbItems("Courses", {}));
    fetchCourseData();
  }, []);

  const fetchCourseData = async (): Promise<void> => {
    try {
      const topicId = null;
      const data = await listCourses(topicId);
      const paidCourses = data.filter(
        (course) => course.course_type === "Paid" && course.is_expired === false
      );
      setCourses(paidCourses);

      setActiveCategory(paidCourses[0]?.category_name || null);
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching courses:", error);
      setIsLoading(false);
    }
  };

  const filteredCourses = courses?.filter((course) => {
    const matchesSearch =
      course.short_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = course.category_name === activeCategory;
    return matchesSearch && matchesCategory;
  });

  // Show confirmation modal when user clicks request
  const handleRequestClick = (course: CoursesParams): void => {
    setSelectedCourse(course);
    setShowConfirmModal(true);
  };

  // Handle the actual course request after confirmation
  const handleConfirmRequest = async (): Promise<void> => {
    if (!selectedCourse) return;
    const userId = localStorage.getItem("userId");
    try {
      const orgId = localStorage.getItem("orgId");
      const data = await requestCourse({
        org_id: orgId as string,
        course_id: selectedCourse.course_id as string,
        requested_by: userId as string,
        resource_id: null,
        resource_type: null,
        status_notes: null,
        updated_plan_id: null,
      });
      console.log("data", data);
      if (data.status === "success") {
        toast({
          variant: "success",
          title: "Success",
          description: SUCCESS_MESSAGES.course_request,
        });
      } else {
        toast({
          variant: "destructive",
          title: "Success",
          description: data.error || "Something went wrong",
        });
      }
      // Close modal and reset state
      setShowConfirmModal(false);
      setSelectedCourse(null);
    } catch (error) {
      console.error("Error requesting course:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: ERROR_MESSAGES.course_request,
      });
    }
  };

  // Handle modal cancellation
  const handleCancelRequest = (): void => {
    setShowConfirmModal(false);
    setSelectedCourse(null);
  };

  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="min-h-screen w-full bg-[var(--color-background)]">
        <div className="w-full px-4 py-6 md:px-6 md:py-8">
          {/* Hero Section */}
          <div className="bg-gradient-to-r from-[var(--color-button-primary)]/10 to-[var(--color-button-info)]/10 rounded-xl  mb-4">
            <div className="max-w-4xl mx-auto text-center">
              <p className="text-2xl mt-4 mb-4 font-bold text-center">
                {t("Start Your Learning Journey")}
              </p>
              <p className="text-lg text-gray-600">
                {t(
                  "Explore hand-picked courses and request access to begin today."
                )}
              </p>
            </div>
          </div>

          {/* Category Tabs */}
          <div className="mb-4 flex flex-wrap gap-3 justify-center">
            {Array.from(new Set(courses?.map((c) => c.category_name))).map(
              (category) => (
                <Button
                  key={category}
                  onClick={() => setActiveCategory(category)}
                  className={`px-5 py-2 rounded-full border text-sm font-semibold transition-all ${
                    activeCategory === category
                      ? "bg-[#9fc089] text-white shadow-md"
                      : "bg-white text-gray-600 border-gray-300 hover:bg-gray-100"
                  }`}
                >
                  {category}
                </Button>
              )
            )}
          </div>

          {/* Search Bar */}
          <div className="relative mb-8 max-w-xl mx-auto">
            <Input
              placeholder={`${t("Search courses...")}`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-4 pr-4 py-3 bg-white border border-gray-200 rounded-lg shadow-sm focus:border-[var(--color-button-primary)] focus:ring-2 focus:ring-[var(--color-button-primary)] focus:ring-opacity-20 text-base font-medium placeholder:text-gray-400 text-[var(--color-font-color)]"
            />
          </div>

          {/* Course Cards */}
          {isLoading ? (
            <div className="text-center py-16">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 border-t-[var(--color-button-primary)] mx-auto"></div>
              <p className="mt-4 text-[var(--color-font-color)] text-lg">
                {t("Loading courses...")}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {filteredCourses?.map((course) => (
                <Card
                  key={course.id}
                  className="bg-white rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300"
                >
                  <div className="h-40 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                    <img
                      src={"/assets/course-request.jpg"} // Replace with actual image key or a default
                      alt={course.short_name}
                      className="object-cover w-[300px] h-[160px]"
                    />
                  </div>
                  <CardContent className="p-4">
                    <h2 className="text-lg font-bold text-gray-800 mb-1 line-clamp-1">
                      {course.short_name}
                    </h2>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: course.summary || t("No description available"),
                      }}
                    />

                    <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                      <span className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-400" />
                        4.5 (120)
                      </span>
                      {/* <span className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    12 hrs
                  </span> */}
                    </div>

                    {/* Action Button */}
                    <div className="pt-4 border-t border-gray-100 mt-auto">
                      <Button
                        className="w-full mt-3  text-sm font-semibold py-2.5 rounded-md transition"
                        variant={"default"}
                        onClick={() => handleRequestClick(course)}
                      >
                        {t("Request Course")}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Course Request Confirmation Modal */}
        {showConfirmModal && (
          <Modal
            title=""
            header=""
            openDialog={showConfirmModal}
            closeDialog={() => setShowConfirmModal(false)}
            type="max-w-lg"
          >
            <CourseRequestConfirm
              course={selectedCourse}
              onConfirm={handleConfirmRequest}
              onCancel={handleCancelRequest}
            />
          </Modal>
        )}
      </div>
    </MainLayout>
  );
}
