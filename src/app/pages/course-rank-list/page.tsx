"use client";
import React, { useEffect, useState } from "react";
import MainLayout from "../layouts/mainLayout";
import "../../../styles/main.css";
import { Card } from "@/components/ui/card";
import Image from "next/image";
import { BADGE_FIRST, BADGE_SECOND, BADGE_THIRD } from "@/lib/constants";
import { Circle, GraduationCap, Square } from "lucide-react";
import { useExam } from "@/hooks/useExam";
import { useTranslation } from "next-i18next";
import { useSearchParams } from "next/navigation";
import {
  CourseDetailsResultType,
  ErrorCatch,
  InnerItem,
  RankListData,
  RankListRequest,
  ToastType,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { Separator } from "@/components/ui/separator";
import { KEYS } from "@/lib/keys";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ERROR_MESSAGES } from "@/lib/messages";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";

export default function CourseRankList(): React.JSX.Element {
  const { t } = useTranslation("common");
  const { toast } = useToast() as unknown as ToastType;
  const { getCourseRankList } = useExam();
  const searchParams = useSearchParams();
  const courseId = searchParams?.get("course_id") as string;
  const [topThree, setTopThree] = useState<RankListData[]>([]);
  const [remainingRankList, setRemainingRankList] = useState<RankListData[]>(
    []
  );
  const orgID = localStorage.getItem(KEYS.ORG_ID);
  const courseID = localStorage.getItem(KEYS.COURSE_ID);
  const [selectedCourse, setSelectedCourse] = useState<string | null>(null);
  const [courseData, setCourseDetails] = useState<CourseDetailsResultType[]>(
    []
  );
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  useEffect(() => {
    setBreadcrumbItems(getBreadCrumbItems("Course Rank List", {}));
  }, []);

  const fetchCourseRankList = async (courseId: string): Promise<void> => {
    const params: RankListRequest = {
      org_id: orgID as string,
      course_id: courseId as string,
      quiz_type_filter: "Main",
    };
    try {
      const response = await getCourseRankList(params);
      if (response?.status === "success") {
        const sortedRankList = response?.rank_list?.sort(
          (a, b) => b.total_sum_grades - a.total_sum_grades
        );
        setTopThree(sortedRankList?.slice(0, 3));
        setRemainingRankList(sortedRankList?.slice(3, 13)); // Display next 10 performers
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: err?.message,
      });
    }
  };

  const handleSelectionChange = (value: string) => {
    fetchCourseRankList(value);
    setSelectedCourse(value);
  };

  useEffect(() => {
    const courseDatas = localStorage.getItem("courseData");
    let data = JSON.parse(courseDatas as string);
    if (courseDatas) {
      setCourseDetails(data);
    }
    if (data.length === 1) {
      fetchCourseRankList(data[0].course_id);
      // getAttemptedQuizesList(data[0].course_id);
      setSelectedCourse(data[0].course_id);
    }
    const courseID = localStorage.getItem(KEYS.COURSE_ID);
    if (courseID) {
      setSelectedCourse(courseID);
      fetchCourseRankList(courseID);
      // getAttemptedQuizesList(courseID);
    }
  }, []);
  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="mb-6">
        <Select
          value={selectedCourse || ""}
          onValueChange={handleSelectionChange}
        >
          <SelectTrigger className="w-full max-w-md mx-auto">
            <SelectValue placeholder={t("Select a Course")}>
              {selectedCourse
                ? courseData.find((c) => c.course_id === selectedCourse)
                    ?.full_name
                : "Select a Course"}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {courseData.map((course) => (
              <SelectItem
                key={course.course_id}
                value={course.course_id}
                className="hover:bg-gray-100"
              >
                {course.full_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="flex flex-col gap-8">
        {topThree?.length > 0 ? (
          <Card className="bg-[#F6E5D3] rounded-lg mt-2">
            <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 p-4 gap-8 pr-6">
              {topThree?.map((item, index) => {
                let badgeImage = null;
                if (index === 0) {
                  badgeImage = BADGE_FIRST;
                } else if (index === 1) {
                  badgeImage = BADGE_SECOND;
                } else if (index === 2) {
                  badgeImage = BADGE_THIRD;
                }
                return (
                  <div key={index} className="relative">
                    <Card className="p-4">
                      <div className="grid grid-cols-6 gap-4">
                        <div className="relative w-16 h-16 items-center">
                          <Image
                            src={item?.avatar_url || ""}
                            alt="Card Image"
                            width={64}
                            height={64}
                            className="rounded-lg"
                          />
                          <div className="absolute bottom-0 right-0 translate-x-1/2 translate-y-1/2 flex items-center justify-center">
                            <Circle className="fill-[#00AFBB] text-[#00AFBB]" />
                            <span className="text-white absolute">
                              {index + 1}
                            </span>
                          </div>
                        </div>
                        <div className="ps-6 col-start-2 col-span-4 gap-4">
                          <div className="grid grid-rows-2 grid-flow-col gap-4">
                            <div className="flex justify-between">
                              <div>{item.user_name}</div>
                              <div className="text-[#00AFBB]">
                                <b>{item.total_sum_grades}</b>
                              </div>
                            </div>
                            <div className="text-[#FB8500]">
                              {t("Exam Attended")} : <b>{item.attempts}</b>
                            </div>
                          </div>
                        </div>
                        {badgeImage && (
                          <div className="absolute top-6 right-badge z-10">
                            <Image
                              src={badgeImage ?? ""}
                              alt="Badge Image"
                              width={45}
                              height={45}
                            />
                          </div>
                        )}
                      </div>
                    </Card>
                  </div>
                );
              })}
            </div>
          </Card>
        ) : (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <GraduationCap className="mx-auto h-16 w-16 text-gray-400 mb-4" />
            <p className="text-xl text-[var(--color-font-color)]">
              {" "}
              {t("No Rank List Found")}
            </p>
          </div>
        )}

        {remainingRankList?.length > 0 && (
          <div className="flex flex-col gap-4">
            <h3> {t("Top 10 Performers")}</h3>
            {remainingRankList.map((item, index) => (
              <React.Fragment key={index}>
                <Card className="p-4 bg-white rounded-lg border-none shadow-none">
                  <div className="grid w-full grid-cols-4 gap-4">
                    <div className="col-span-1 relative">
                      <Image
                        src={item.avatar_url}
                        alt="Card Image"
                        width={60}
                        height={60}
                        className="rounded-full"
                      />
                      <div className="absolute z-10 left-12 bottom-0.5">
                        <Square className="fill-[#00AFBB] text-[#00AFBB]" />
                        <span className="text-white absolute left-1.5 bottom-rank">
                          {index + 4}
                        </span>
                      </div>
                    </div>
                    <div className="col-span-3">
                      <div className="flex flex-col">
                        <div className="grid grid-cols-2 pt-3">
                          <div>{item.user_name}</div>
                        </div>
                        <div className="grid grid-cols-2 pt-3">
                          <div className="text-[#FB8500]">
                            {t("Mark")} : <b>{item.total_sum_grades}</b>
                          </div>
                          <div className="text-[#FB8500]">
                            {t("Exam Attended")} : <b>{item.attempts}</b>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
                <Separator />
              </React.Fragment>
            ))}
          </div>
        )}
      </div>
    </MainLayout>
  );
}
