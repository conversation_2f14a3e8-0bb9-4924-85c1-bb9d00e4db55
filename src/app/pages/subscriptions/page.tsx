"use client";
import React, { useEffect, useState } from "react";
import { Carousel } from "react-responsive-carousel";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import MainLayout from "../layouts/mainLayout";
import { Check, Eye } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { cardColor } from "@/lib/constants";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import AddPlanConfirmation from "./addPlanModal";
import { useSubscription } from "@/hooks/useSubscription";
import {
  CourseResourceReturn,
  InnerItem,
  LoginUserData,
  SubscriptionResultType,
} from "@/types";
import { KEYS } from "@/lib/keys";
import { useMediaQuery } from "react-responsive";
import {
  APPROVE_TEXT,
  currencySymbols,
  PENDING_TEXT,
  SELECT_TEXT,
} from "@/lib/constants";
import { ERROR_MESSAGES } from "@/lib/messages";
import CourseDetails from "./courseDetails";
import { USER_DATA } from "@/lib/utils";
import "../../../styles/main.css";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { useTranslation } from "next-i18next";
export default function Subscriptions(): React.JSX.Element {
  const { t } = useTranslation("common");
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);
  const [isOpenCourseModal, setIsOpenCourseModal] = useState<boolean>(false);
  const [subscriptionData, setSubscriptionData] = useState<
    SubscriptionResultType[]
  >([]);
  const [filteredSubscriptionData, setFilteredSubscriptionData] = useState<
    SubscriptionResultType[]
  >([]);
  const [currentData, setCurrentData] = useState<SubscriptionResultType>();
  const [description, setDescription] = useState<string[]>();
  const [searchQuery, setSearchQuery] = useState<string>("");
  const { getSubscriptions, getResourceList } = useSubscription();
  const [activeIndex, setActiveIndex] = useState<number>(1);
  const [planId, setPlanId] = useState<string>("");
  const isMobileOrTablet = useMediaQuery({ maxWidth: 1024 });
  const [courseData, setCourseData] = useState<CourseResourceReturn>();
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);

  const centerSlidePercentage =
    filteredSubscriptionData.length === 1
      ? 33.33
      : isMobileOrTablet
      ? 100
      : 33.33;

  useEffect(() => {
    getSubscriptionDetails();

    setBreadcrumbItems(getBreadCrumbItems("Subscription", {}));
  }, []);

  useEffect(() => {
    if (subscriptionData.length > 0) {
      setActiveIndex(1);
    }
  }, [subscriptionData]);

  useEffect(() => {
    // Filter subscription data whenever the search query changes
    if (searchQuery.trim() === "") {
      setFilteredSubscriptionData(subscriptionData);
      setActiveIndex(1);
      setCurrentData(subscriptionData[1]);
    } else {
      let displayData = subscriptionData.filter((item) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredSubscriptionData(displayData);
      setCurrentData(displayData[0]);
      setDescription(splitStringIntoArray(displayData[0]?.description));
      setActiveIndex(0);
    }
  }, [searchQuery]);

  const getSubscriptionDetails = async () => {
    try {
      if (USER_DATA) {
        const userInfo = JSON.parse(USER_DATA) as LoginUserData;
        const userId = userInfo.id;
        const orgID = localStorage.getItem(KEYS.ORG_ID);
        if (orgID) {
          const res = await getSubscriptions(orgID, userId);
          const uniqueData = Array.from(
            new Map(res.result.map((item) => [item.id, item])).values()
          );
          setSubscriptionData(uniqueData);
          setFilteredSubscriptionData(uniqueData);
          if (uniqueData.length > 0) {
            if (uniqueData.length === 1) {
              setCurrentData(uniqueData[0]);
              setDescription(splitStringIntoArray(uniqueData[0].description));
            } else {
              setCurrentData(uniqueData[1]);
              setDescription(splitStringIntoArray(uniqueData[1].description));
            }
          }
        }
      }
    } catch (error) {
      console.error("Error fetching subscription details:", error);
    }
  };
  const getCourseDetails = async (id: string) => {
    try {
      const orgID = localStorage.getItem(KEYS.ORG_ID);
      if (orgID) {
        const res = await getResourceList(orgID, id);
        setCourseData(res);
      }
      setIsOpenCourseModal(true);
    } catch (error) {
      console.error("Error fetching subscription details:", error);
    }
  };
  const setDispalyItem = (index: number) => {
    const item = filteredSubscriptionData[index];
    setCurrentData(item);
    setDescription(splitStringIntoArray(item.description));
  };
  const splitStringIntoArray = (str: string) => {
    return str
      ? str.split("\n").filter((segment) => segment.trim() !== "")
      : [];
  };
  const getButtonText = (item: SubscriptionResultType) => {
    if (!item.is_user_subscribed) {
      return SELECT_TEXT;
    }
    switch (item.subscription_usage_status) {
      case "pending":
        return PENDING_TEXT;
      case "approved":
        return APPROVE_TEXT;
      default:
        return PENDING_TEXT;
    }
  };

  const getButtonClasses = (item: SubscriptionResultType) => {
    if (!item.is_user_subscribed) {
      return "bg-black text-white";
    }
    switch (item.subscription_usage_status) {
      case "pending":
        return "bg-yellow-500 text-white";
      case "approved":
        return "bg-green-700 text-white";
      default:
        return "bg-black text-white";
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };
  const getRandomCardColor = () => {
    const randomIndex = Math.floor(Math.random() * cardColor.length);
    return cardColor[randomIndex];
  };
  const getCurrencySymbol = (currencyCode: any) => {
    return currencySymbols[currencyCode] || currencyCode;
  };
  const itemTemplate = (item: SubscriptionResultType, index: number) => {
    const { bg, border } = getRandomCardColor();
    const isActive = activeIndex === index;

    const purchasePlan = (item: SubscriptionResultType, event: any) => {
      if (!item.is_user_subscribed) {
        setPlanId(item.id);
        setIsOpenModal(true);
      }
    };

    return (
      <div
        key={item.id}
        className={`p-1 w-full transition-transform duration-300 ${
          isActive ? "transform scale-100" : "transform scale-90"
        }`}
      >
        <Card
          style={{ backgroundColor: bg, borderColor: border }}
          className="border w-full"
        >
          <CardContent className="flex flex-col items-center p-4 md:p-6">
            <span className="text-lg font-semibold text-center mb-2">
              {capitalizeFirstWord(item.subscription_frequency)}
            </span>
            <div className="text-center text-2xl md:text-3xl mb-2">
              {getCurrencySymbol(item.currency)}
              {item.price}
            </div>
            <div className="text-center">
              <Button
                className={getButtonClasses(item)}
                onClick={(event) => purchasePlan(item, event)}
                variant="default"
              >
                {getButtonText(item)}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };
  const closeModal = () => {
    setIsOpenModal(false);
  };
  const closeCourseModal = () => {
    setIsOpenCourseModal(false);
  };
  //to capitalize the card header name
  function capitalizeFirstWord(str: string) {
    if (!str) return str;
    const words = str.split(" ");
    words[0] = words[0].charAt(0).toUpperCase() + words[0].slice(1);
    return words.join(" ");
  }
  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="container mx-auto px-4 py-6 flex flex-col items-center">
        <p className="text-2xl mt-4 mb-4 font-bold text-center">
          {t("Upgrade your membership")}
        </p>
        {/* Search Box */}
        <div className="mb-4 border border-gray h-10 w-full md:w-1/4 flex justify-center">
          <input
            type="text"
            placeholder={t("Search for a plan...")}
            value={searchQuery}
            onChange={handleSearchChange}
            className="w-full px-2"
          />
        </div>

        {/* Conditional Content */}
        <div>
          {filteredSubscriptionData.length === 0 ? (
            <div className="text-center text-xl font-semibold mt-4">
              <div className="flex justify-center items-center mb-6">
                <img src="/assets/no-data.png" className="md:h-64 pt-10" />
              </div>

              {t("noPlanText")}
            </div>
          ) : (
            <>
              <div className="flex justify-center items-center mb-6">
                <img
                  src="/assets/subscription-icon.png"
                  className="w-40 h-48 md:w-52 md:h-64"
                />
              </div>
              <div className="relative flex flex-col items-center mt-4">
                <p className="mt-4 mb-4 font-bold text-xl lg:text-2xl flex items-center">
                  {currentData?.name}
                  <span
                    className="ml-2 lg:ml-16 cursor-pointer"
                    onClick={() => {
                      getCourseDetails(currentData?.id as string);
                    }}
                  ></span>
                </p>

                <ScrollArea className="bg-[#E8AE01] w-full max-w-screen-xl h-44 rounded-2xl p-4 overflow-y-auto mb-4">
                  <div className="p-4">
                    {description?.map((item, index) => (
                      <div
                        key={index}
                        className="grid grid-cols-[auto,1fr] items-start mb-2 last:mb-0"
                      >
                        <Check className="border rounded-md text-white" />
                        <div className="ml-4">
                          <div className="flex items-center">
                            <span className="ml-2 mb-2 text-white lg:text-xl">
                              {item}
                            </span>
                          </div>
                          {index < description.length - 1 && <Separator />}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>

                {/* Carousel */}
                <div className="relative w-full max-w-screen-lg">
                  <Carousel
                    showThumbs={false}
                    showStatus={false}
                    showIndicators={false}
                    centerMode={true}
                    centerSlidePercentage={centerSlidePercentage}
                    selectedItem={activeIndex}
                    onChange={(index) => {
                      setActiveIndex(index);
                      setDispalyItem(index);
                    }}
                    autoPlay={false}
                    interval={5000}
                    className="mb-8"
                  >
                    {filteredSubscriptionData.map((item, index) =>
                      itemTemplate(item, index)
                    )}
                  </Carousel>

                  {/* Dots */}
                  <div className="absolute bottom-0 left-0 right-0 flex justify-center space-x-2 mt-8 md:mt-12">
                    {filteredSubscriptionData.map((_, index) => (
                      <span
                        key={index}
                        className={`inline-block w-3 h-3 rounded-full cursor-pointer ${
                          activeIndex === index ? "bg-[#E8AE01]" : "bg-gray-400"
                        }`}
                        onClick={() => {
                          setActiveIndex(index);
                          setDispalyItem(index);
                        }}
                      ></span>
                    ))}
                  </div>
                </div>
                <div className="left-0 right-0 flex justify-center space-x-2 mt-12 pt-15">
                  {/* <p className="text-xl">
                    We offer pricing plans tailored to your needs. Find the
                    perfect fit for your budget and goals.
                  </p> */}
                  <p className="text-xl">
                    {t(
                      "We offer pricing plans tailored to your needs. Find the"
                    )}{" "}
                    <br />
                    {t("perfect fit for your budget and goals")}
                  </p>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Modals */}
        {isOpenModal && (
          <AddPlanConfirmation
            status="Success"
            closeDialog={closeModal}
            id={planId}
          />
        )}
        {isOpenCourseModal && (
          <CourseDetails
            status="Success"
            closeDialog={closeCourseModal}
            data={courseData}
          />
        )}
      </div>
    </MainLayout>
  );
}
