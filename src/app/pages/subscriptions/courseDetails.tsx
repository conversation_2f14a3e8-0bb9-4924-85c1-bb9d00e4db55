"use client";
import { CourseResourceReturn } from "@/types";
import React from "react";
import { X } from "lucide-react";
import { useTranslation } from "next-i18next";

interface CourseDetailsProps {
  data?: CourseResourceReturn;
  status: string;
  closeDialog: () => void;
}

export default function CourseDetails({
  data,
  closeDialog,
}: CourseDetailsProps): React.JSX.Element {
  const closeModal = () => {
    closeDialog();
  };
  const { t } = useTranslation("common");
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="relative bg-white shadow-md rounded-lg p-6 w-[900px] h-[600px] overflow-y-auto border border-gray-300 pt-8">
        <button
          onClick={closeModal}
          className="absolute top-4 right-4 p-2 text-gray-600 hover:text-gray-800"
          aria-label="Close"
        >
          <X size={24} />
        </button>
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          {" "}
          {t("Courses")} :
        </h1>
        {data && data.result.length > 0 ? (
          data.result.map((course) => (
            <div key={course.course_name} className="mb-6">
              <h1 className="text-xl font-bold text-gray-800 mb-4">
                {course.course_name}
              </h1>
              <ul className="list-disc pl-5">
                {course.resources.map((resource) => (
                  <li key={resource.resource_id} className="mb-2">
                    <div className="font-semibold">{resource.name}</div>
                  </li>
                ))}
              </ul>
            </div>
          ))
        ) : (
          <div className="text-center text-gray-500">
            {t("No course details available")}
          </div>
        )}
      </div>
    </div>
  );
}
