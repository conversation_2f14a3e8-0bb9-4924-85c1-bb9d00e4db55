"use client";
import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import AuthLayout from "../layouts/authlayout";
import { SignUpFormSchema } from "@/schema/schema";
import { useForm } from "react-hook-form";
import { useTranslation } from "next-i18next";
import {
  ErrorCatch,
  SignupFormType,
  SignUpRequestType,
  ToastType,
} from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import "../../../styles/main.css";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import useAuthorization from "@/hooks/useAuth";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { useRouter } from "next/navigation";
const Signup: React.FC = () => {
  const form = useForm<SignupFormType>({
    resolver: zodResolver(SignUpFormSchema),
  });
  const { signUpUser } = useAuthorization();
  const { toast } = useToast() as ToastType;
  const router = useRouter();
  async function onSubmit(data: SignupFormType): Promise<void> {
    let passData = {
      email: data.email,
      password: data.password,
      data: {
        first_name: data.firstName,
        last_name: data.lastName,
        phone_number: data.phoneNumber,
      },
    };
    try {
      const result = await signUpUser(passData as SignUpRequestType);
      if (result) {
        toast({
          variant: "default",

          title: t("success"),

          description: t("signUp"),
        });
        router.push("/pages/login");
      } else {
        toast({
          variant: "destructive",

          title: t("error"),

          description: t("already_exists"),
        });
      }
    } catch (error) {
      const err = error as Error;
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: err?.message,
      });
    }
  }
  const { t } = useTranslation("common");
  return (
    <AuthLayout>
      <div
        className="bg-teal-800 p-8 rounded-lg mx-auto shadow-lg sm:w-[350px]   max-w-sm w-full"
        style={{ background: "#FFFFFF" }}
      >
        <h1 className="text-2xl font-bold text-black mb-4 text-center">
          ({t("Create An Account")})
        </h1>
        <Form {...form}>
          <form
            onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
            className="max-w-xl mx-auto"
          >
            <div className="mb-4">
              <FormField
                name="firstName"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        id="firtName"
                        type="text"
                        placeholder={t("First Name")}
                        className="w-full rounded-md py-2 px-4"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              ></FormField>
            </div>
            <div className="mb-4">
              <FormField
                name="lastName"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        id="lastname"
                        type="text"
                        placeholder={t("Last Name")}
                        {...field}
                        className="w-full rounded-md py-2 px-4"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              ></FormField>
            </div>
            <div className="mb-4">
              <FormField
                name="email"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        id="email"
                        type="email"
                        placeholder={t("Email")}
                        autoCorrect="off"
                        {...field}
                        className="w-full rounded-md py-2 px-4"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              ></FormField>
            </div>
            <div className="mb-4">
              <FormField
                name="password"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        id="password"
                        type="password"
                        placeholder={t("Password")}
                        {...field}
                        className="w-full rounded-md py-2 px-4"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              ></FormField>
            </div>
            <div className="mb-4">
              <FormField
                name="confirmPassword"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        id="confirm"
                        type="password"
                        placeholder={t("Confirm Password")}
                        {...field}
                        className="w-full rounded-md py-2 px-4"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              ></FormField>
            </div>
            <div className="mb-4">
              <FormField
                name="phoneNumber"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        id="phone"
                        type="tel"
                        placeholder={t("Phone Number")}
                        {...field}
                        onChange={(e) => {
                          const onlyDigits = e.target.value.replace(/\D/g, "");
                          if (onlyDigits.length <= 10) {
                            field.onChange(onlyDigits);
                          }
                        }}
                        className="w-full rounded-md py-2 px-4"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              ></FormField>
            </div>
            <div className="mb-8">
              <Button
                type="submit"
                className="w-full rounded-md py-2 px-4 bg-[#ffa500] hover:bg-[#ffa500] text-white"
              >
                {t("Sign Up")}
              </Button>
              <div className="px-4 text-center text-sm text-muted-foreground mt-3 ">
                <p className="text-base">
                  {t("Already have an account?")}
                  <Link
                    href="/pages/login"
                    className="ml-1 underline underline-offset-4 hover:text-primary "
                  >
                    <span className="navigation-link-color text-base">
                      {t("Login")}
                    </span>
                  </Link>
                </p>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </AuthLayout>
  );
};

export default Signup;
