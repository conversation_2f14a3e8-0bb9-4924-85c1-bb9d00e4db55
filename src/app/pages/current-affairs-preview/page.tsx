"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import useCurrentAffairs from "@/hooks/useCurrentAffairs";
import { currentAffairsResponse } from "@/types";
import { KEYS } from "@/lib/keys";
import { useRouter } from "next/navigation";
import { Carousel } from "primereact/carousel";
import { useTranslation } from "next-i18next";

const CurrentAffairsCarousel = () => {
  const { currentAffairs } = useCurrentAffairs();
  const { t } = useTranslation("common");
  const router = useRouter();
  const [currentAffairData, setCurrentAffairData] = useState<
    currentAffairsResponse[]
  >([]);
  const [loading, setLoading] = useState(true);
  let visibleItemCount: number;

  const mobResponsive = [
    {
      breakpoint: "1024px",
      numVisible: 3,
      numScroll: 1,
    },
    {
      breakpoint: "768px",
      numVisible: 2,
      numScroll: 1,
    },
    {
      breakpoint: "560px",
      numVisible: 1,
      numScroll: 1,
    },
  ];

  useEffect(() => {
    const configData = JSON.parse(
      localStorage.getItem("configurations") || "{}"
    );
    visibleItemCount = configData?.current_affairs?.no_of_items;
    const fetchSessionData = async () => {
      try {
        const result = await currentAffairs();
        if (result) {
          setCurrentAffairData(result.slice(0, visibleItemCount));
          setLoading(false);
        }
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    };
    fetchSessionData();
  }, []);
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleString("default", { month: "short" });
    const year = date.getFullYear();
    return `${day} ${month} ${year}`;
  };

  const currentAffairTemplate = (item: currentAffairsResponse) => {
    return (
      <div
        className="p-4 bg-white rounded-md cursor-pointer relative overflow-hidden mx-2 h-48 text-[var(--color-font-color)]"
        onClick={() => {
          const data = { title: item.title, content: item.content };
          localStorage.setItem(
            KEYS.CURRENT_AFFAIR_CONSTANT,
            JSON.stringify(data) ?? ""
          );
          router.push(
            `/pages/current-affair-details?title=${formatDate(
              item.publish_date
            )}`
          );
        }}
      >
        <Image
          src={"/assets/current-affairs.png"}
          alt={item.title}
          layout="fill"
          objectFit="cover"
          className="absolute inset-0 w-full h-full"
        />
        <div className="relative p-4 text-white">
          <p className="text-xs">{formatDate(item.publish_date)}</p>
          <h3 className="text-sm sm:text-base font-semibold mt-1 line-clamp-2">
            {item.title}
          </h3>
        </div>
      </div>
    );
  };

  return (
    <div className="pb-2 w-full">
      <div className="flex items-center justify-between w-full">
        <h2 className="text-2xl text-[var(--color-font-color)] font-bold pb-0">
          {t("Current Affairs")}
        </h2>

        <div className="flex items-center">
          <a
            href="/pages/current-affairs"
            className="text-[var(--color-nav-text)] hover:text-[var(--color-nav-hover-text)] font-semibold underline text-base ml-2 mt-1"
          >
            {t("View All")}
          </a>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center py-6 sm:py-8">
          <div className="animate-pulse text-sm sm:text-base">
            {t("Loading current affairs")}
          </div>
        </div>
      ) : currentAffairData.length > 0 ? (
        <div className="pt-4">
          <Carousel
            value={currentAffairData}
            numVisible={4}
            numScroll={1}
            responsiveOptions={mobResponsive}
            itemTemplate={currentAffairTemplate}
            className="w-full"
            showNavigators={currentAffairData.length > 4}
          />
        </div>
      ) : (
        <div className="w-full flex flex-col py-6 sm:py-8 items-center">
          <div className="text-sm sm:text-base font-semibold p-3 sm:p-4 text-center">
            {t("No current affairs available")}
          </div>
        </div>
      )}
    </div>
  );
};

export default CurrentAffairsCarousel;
