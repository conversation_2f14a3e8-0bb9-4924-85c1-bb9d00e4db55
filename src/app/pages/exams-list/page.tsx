"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FilePlus2, FileSearch, NotebookText, Plus } from "lucide-react";
import React, { useState, useEffect } from "react";
import MainLayout from "../layouts/mainLayout";
import "../../../styles/main.css";
import { useRouter } from "next/navigation";
import { useExam } from "@/hooks/useExam";
import { useTranslation } from "next-i18next";
import {
  AttemptedIdsOfQuiz,
  AttemptedQuizRequest,
  AttemptedQuizResponse,
  ErrorCatch,
  InnerItem,
  LoginUserData,
  QuizRequest,
  QuizResponse,
  SubmitQuizType,
  ToastType,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { Separator } from "@/components/ui/separator";
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { KEYS } from "@/lib/keys";
import { getLocalStorageItem } from "@/lib/utils";
import { ERROR_MESSAGES } from "@/lib/messages";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";

export default function ExamsList(): React.JSX.Element {
  const { t } = useTranslation("common");
  const router = useRouter();
  const { getCourseQuizes, examResultAnalysis, evaluateAnswer } = useExam();
  const { toast } = useToast() as unknown as ToastType;

  const [quizData, setQuizData] = useState<QuizResponse[]>([]);
  const [attemptedQuizData, setAttemptedQuizData] = useState<
    AttemptedQuizResponse[]
  >([]);
  const [passedQuizData, setPassedQuizData] = useState<AttemptedQuizResponse[]>(
    []
  );
  const searchParams = useSearchParams();
  const tab = searchParams?.get("tab") as string;
  const [activeTab, setActiveTab] = useState("newExams");
  const [defaultTab, setDefaultTab] = useState("newExams");
  const courseId = searchParams?.get(KEYS.COURSE_ID) as string;
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const orgID = localStorage.getItem(KEYS.ORG_ID);
  const handleAttend = (item: QuizResponse) => {
    router.push(`/pages/examsIntro?quiz_id=${item.id}&course_id=${courseId}`);
  };
  const handleReAttend = (item: AttemptedQuizResponse) => {
    localStorage.removeItem(KEYS.ANSWER_EVALUATION);
    router.push(
      `/pages/examsIntro?quiz_id=${item.quiz_id}&course_id=${courseId}`
    );
  };
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };
  const USER_DATA = getLocalStorageItem(KEYS.USER_DETAILS);
  useEffect(() => {
    if (tab) {
      setDefaultTab(tab);
      handleTabChange(tab);
    }

    setBreadcrumbItems(
      getBreadCrumbItems("Exam List", {
        course_id: courseId,
      })
    );
  }, []);

  useEffect(() => {
    localStorage.removeItem(KEYS.ANSWER_EVALUATION);
    getQuizesList();
    getAttemptedQuizesList();
  }, [courseId]);

  const getQuizesList = async (): Promise<void> => {
    if (USER_DATA !== null && USER_DATA !== undefined) {
      const userInfo = JSON.parse(USER_DATA) as LoginUserData;
      const params: QuizRequest = {
        course_id: courseId,
        org_id: orgID ?? "",
        user_id: userInfo.id,
        quizes_of_course_data: {
          quiz_type: ["Main", "Practice"],
        },
      };
      try {
        const result = await getCourseQuizes(params);
        const quizesList = result.filter(
          (exam) => exam.publish_status === "Published"
        );
        setQuizData(quizesList);
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: "destructive",
          title: ERROR_MESSAGES.error,
          description: err?.message,
        });
      }
    }
  };

  const getAttemptedQuizesList = async (): Promise<void> => {
    if (USER_DATA !== null && USER_DATA !== undefined) {
      const userInfo = JSON.parse(USER_DATA) as LoginUserData;
      const params: AttemptedQuizRequest = {
        course_id: courseId as string,
        org_id: orgID as string,
        user_id: userInfo.id as string,
        type_of_quiz: "Practice",
      };

      try {
        const quizesList = await examResultAnalysis(params);
        setAttemptedQuizData(quizesList);
        const passedResults = quizesList?.filter(
          (item) => item.result === "Passed"
        );

        passedResults?.map((item) => {
          item.passed_date = getDateByQuizAttemptId(
            item.quiz_attempt_id as unknown as string,
            item.attempt_ids_of_quiz
          ) as string;
        });
        setPassedQuizData(passedResults);
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: "destructive",
          title: ERROR_MESSAGES.error,
          description: err?.message,
        });
      }
    }
  };

  const getDateByQuizAttemptId = (
    attemptId: string,
    ids: AttemptedIdsOfQuiz[]
  ) => {
    const foundAttempt = ids.find(
      (item: { attempt_id: string }) => item.attempt_id === attemptId
    );
    return foundAttempt ? foundAttempt.date : null; // Return the date or null if not found
  };
  const handleCancel = (): void => {
    // Check if we came from subject-details page using URL parameters
    const sectionId = searchParams?.get('section_id');
    const fromPage = searchParams?.get('from');

    if (fromPage === 'subject-details' && sectionId) {
      router.push(`/pages/subject-details?section_id=${sectionId}&course_id=${courseId}`);
      return;
    }

    if (process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true") {
      router.push(`/pages/section-details`);
    } else {
      router.push(`/pages/course-details?course_id=${courseId}`);
    }
  };
  const handleRankList = (item: AttemptedQuizResponse) => {
    router.push(
      `/pages/exam-rank-list?quiz_id=${item.quiz_id}&course_id=${courseId}&tab=${activeTab}`
    );
  };

  const viewExamResult = (item: AttemptedQuizResponse) => {
    localStorage.removeItem(KEYS.ANSWER_EVALUATION);
    const validAttempts = item.attempt_ids_of_quiz.filter(
      (attempt) => attempt.end_time !== null
    );

    const passData = {
      quiz_id: item.quiz_id as string,
      quiz_attempt_id: validAttempts[0].attempt_id,
    };

    const fetchData = async (): Promise<void> => {
      try {
        const questionsData = await evaluateAnswer(passData as SubmitQuizType);

        localStorage.setItem(
          KEYS.ANSWER_EVALUATION,
          JSON.stringify(questionsData)
        );
        router.push(
          `/pages/examResultAnalysis?quiz_id=${item.quiz_id}&quiz_attempt_id=${
            item.quiz_attempt_id
          }&course_id=${courseId}&checkpoint_exam=${false}&tab=${activeTab}`
        );
      } catch (error: unknown) {}
    };
    fetchData().catch((error) => console.log(error));
  };

  const getValidEndTime = (attempts: AttemptedIdsOfQuiz[]) => {
    const validAttempts = attempts.filter((attempt) => attempt.end_time);
    const latestAttempt = validAttempts.reduce((latest, current) => {
      return new Date(current.end_time!) > new Date(latest.end_time!)
        ? current
        : latest;
    });
    return new Date(latestAttempt.end_time!).toLocaleDateString("en-US");
  };

  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <Tabs
        defaultValue={defaultTab}
        onValueChange={handleTabChange}
        className="w-full bg-white "
        value={activeTab}
      >
        <TabsList
          color="orange"
          className="grid w-full grid-cols-3 h-15 bg-white"
        >
          <TabsTrigger value="newExams">
            <div className="flex flex-col items-center">
              <FilePlus2
                className={
                  activeTab === "newExams" ? "text-[#00afbb]" : "text-black"
                }
              />
              <span
                className={
                  activeTab === "newExams" ? "text-[#00afbb]" : "text-black"
                }
              >
                {t("New Exams")}
              </span>
              <Separator
                className={`my-2 ${
                  activeTab === "newExams" ? "bg-[#00afbb]" : "bg-transparent"
                }`}
              />
            </div>
          </TabsTrigger>
          <TabsTrigger value="attempted">
            <div className="flex flex-col items-center">
              <FileSearch
                className={
                  activeTab === "attempted" ? "text-[#00afbb]" : "text-black"
                }
              />
              <span
                className={
                  activeTab === "attempted" ? "text-[#00afbb]" : "text-black"
                }
              >
                {t("Attempted")}
              </span>
              <Separator
                className={`my-2 ${
                  activeTab === "attempted" ? "bg-[#00afbb]" : "bg-transparent"
                }`}
              />
            </div>
          </TabsTrigger>
          <TabsTrigger value="passed">
            {" "}
            <div className="flex flex-col items-center">
              <NotebookText
                className={
                  activeTab === "passed" ? "text-[#00afbb]" : "text-black"
                }
              />
              <span
                className={
                  activeTab === "passed" ? "text-[#00afbb]" : "text-black"
                }
              >
                {t("Passed")}
              </span>
              <Separator
                className={`my-2 ${
                  activeTab === "passed" ? "bg-[#00afbb]" : "bg-transparent"
                }`}
              />
            </div>{" "}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="newExams">
          <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1 ">
            {quizData?.length > 0 ? (
              quizData?.map((item) => (
                <Card
                  className="bg-[#FFF8F1] rounded-lg border-none text-[var(--color-font-color)]"
                  key={item.name}
                >
                  <CardHeader>
                    <CardTitle className="flex justify-between items-center font-normal text-base ">
                      <div className="flex-1">
                        <b>{item.name}</b>
                      </div>
                      <div
                        className="bg-[#00AFBB] flex items-center space-x-2 rounded-tl-lg rounded-r-lg text-white cursor-pointer"
                        onClick={() => handleAttend(item)}
                      >
                        <Plus className="p-1 w-5 h-5" type="button" />
                      </div>
                      <span className="p-1 text-[#00AFBB]"> {t("Attend")}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Card className="py-4 px-4 border-[#FDB666] bg-[#FFF8F1] rounded-lg border-2 ">
                      <div className="flex justify-between py-2">
                        <div>
                          <p className="text-[var(--color-font-color)]">
                            <div>
                              {t("Total No: of Questions")} :{" "}
                              <b>{item.num_of_questions}</b>
                            </div>
                          </p>
                        </div>
                        <div>
                          <p className="text-[var(--color-font-color)]">
                            <div>
                              {" "}
                              {t("Total marks")} : <b>{item.total_mark}</b>
                            </div>
                          </p>
                        </div>
                      </div>
                      <div className="flex justify-between py-2">
                        <div>
                          <p className="text-[var(--color-font-color)] text-[var(--color-font-color)]">
                            <div>
                              {t("Passmark")} : <b>{item.pass_mark}</b>
                            </div>
                          </p>
                        </div>
                        <div>
                          <p className="text-[var(--color-font-color)]">
                            <div>
                              {t("Duration of Exam")} : <b>{item.duration}</b>
                            </div>
                          </p>
                        </div>
                      </div>
                    </Card>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card className="bg-pink-50 shadow-xl rounded-lg border-none">
                <CardContent className="p-2 flex flex-col justify-center items-center text-[var(--color-font-color)]">
                  <span> {t("No New Exam For You")}</span>
                  <Separator className="my-2 w-full border border-slate-300" />
                  {/* <p>
                    {" "}
                    You have attended all new exams. You can re-attend the exams
                    which you have not secured pass mark in the next tab.
                  </p> */}
                  <p>
                    {t(
                      "You have attended all new exams. You can re-attend the exams"
                    )}{" "}
                    <br />
                    {t("which you have not secured pass mark in the next tab")}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
        <TabsContent value="attempted">
          <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1">
            {attemptedQuizData?.length > 0 ? (
              attemptedQuizData?.map((item) => (
                <Card
                  className="bg-[#FFF8F1] rounded-lg border-none w-full"
                  key={item.name}
                >
                  <CardHeader>
                    <CardTitle className="flex justify-between items-center font-normal text-base text-[var(--color-font-color)]">
                      <div className="flex-1">
                        <b>{item.name}</b>
                      </div>
                      {(() => {
                        const isExpired = new Date(item.end_time) < new Date();
                        if (!isExpired) {
                          return (
                            <>
                              <div
                                className="bg-[#00AFBB] flex items-center space-x-2 rounded-tl-lg rounded-r-lg text-white cursor-pointer"
                                onClick={() => handleReAttend(item)}
                              >
                                <Plus className="p-1 w-5 h-5" type="button" />
                              </div>
                              <span className="p-1 text-[#00AFBB]">
                                {t("Re-Attend")}
                              </span>
                            </>
                          );
                        }
                      })()}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Card className="py-4 px-4 border-[#FDB666] bg-[#FFF8F1] rounded-lg border-2">
                      <div className="flex justify-between py-2">
                        <div>
                          <p className="text-[var(--color-font-color)]">
                            <div>
                              {t("Total No: of Questions")} :{" "}
                              <b>{item.num_of_questions}</b>
                            </div>
                          </p>
                        </div>

                        <div>
                          <p className="text-[var(--color-font-color)]">
                            <div>
                              {t("Total marks")} : <b>{item.total_mark}</b>
                            </div>
                          </p>
                        </div>

                        <div>
                          <p className="text-[var(--color-font-color)]">
                            <div>
                              {t("Exam Type")} : <b>{item.quiz_type}</b>
                            </div>
                          </p>
                        </div>
                      </div>
                      <div className="flex justify-between py-2">
                        <div>
                          <p className="text-[var(--color-font-color)]">
                            <div>
                              {t("Passmark")} : <b>{item.pass_mark}</b>
                            </div>
                          </p>
                        </div>

                        <div>
                          <p className="text-[var(--color-font-color)] ml-5">
                            <div>
                              {t("Duration of Exam")} : <b>{item.duration}</b>
                            </div>
                          </p>
                        </div>

                        <div>
                          <p className="text-[var(--color-font-color)]">
                            <div>
                              {t("Attended on")} :{" "}
                              <b>{getValidEndTime(item.attempt_ids_of_quiz)}</b>
                            </div>
                          </p>
                        </div>
                      </div>
                    </Card>
                    <div className="flex flex-col sm:flex-row justify-between w-full space-y-2 sm:space-y-0 sm:space-x-2">
                      <div className="w-full">
                        <Button
                          variant="default"
                          className="w-full rounded-md"
                          onClick={() => viewExamResult(item)}
                        >
                          {t("View Results")}
                        </Button>
                      </div>
                      <div className="w-full">
                        <Button
                          variant="default"
                          className="w-full rounded-md"
                          onClick={() => handleRankList(item)}
                        >
                          {t("Rank List")}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card className="bg-pink-50 shadow-xl rounded-lg border-none">
                <CardContent className="p-2 flex flex-col justify-center items-center text-center text-[var(--color-font-color)]">
                  <span> {t("No attented exams found")}</span>
                  <Separator className="my-2 w-full border border-slate-300" />
                  <p>{t("Attented Exams will be listed here")}</p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="passed">
          <div className="grid gap-4 md:grid-cols-4 sm:grid-cols-1 lg:grid-cols-1">
            {passedQuizData?.length > 0 ? (
              passedQuizData?.map((item) => (
                <Card
                  className="bg-[#FFF8F1] rounded-lg border-none"
                  key={item.name}
                >
                  <CardHeader>
                    <CardTitle className="flex justify-between items-center font-normal text-base">
                      <div className="flex-1">
                        <b>{item.name}</b>
                      </div>
                      {/* <div
                        className="bg-[#00AFBB] flex items-center space-x-2 rounded-tl-lg rounded-r-lg text-white cursor-pointer"
                        onClick={() => handleReAttend(item)}
                      >
                        <Plus className="p-1 w-5 h-5" type="button" />
                      </div> */}
                      {/* <span className="p-1 text-[#00AFBB]">Re-Attend</span> */}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Card className="py-4 px-4 border-[#FDB666] bg-[#FFF8F1] rounded-lg border-2">
                      <div className="flex justify-between py-2">
                        <div>
                          <p className="text-[var(--color-font-color)]">
                            <div>
                              {" "}
                              {t("Total No: of Questions")} :{" "}
                              <b>{item.num_of_questions}</b>
                            </div>
                          </p>
                        </div>

                        <div>
                          <p className="text-[var(--color-font-color)]">
                            <div>
                              {" "}
                              {t("Total marks")} : <b>{item.total_mark}</b>
                            </div>
                          </p>
                        </div>

                        <div>
                          <p className="text-[var(--color-font-color)]">
                            <div>
                              {t("Exam Type")} : <b>{item.quiz_type}</b>
                            </div>
                          </p>
                        </div>
                      </div>
                      <div className="flex justify-between py-2">
                        <div>
                          <p className="text-[var(--color-font-color)]">
                            <div>
                              {" "}
                              {t("Passmark")} : <b>{item.pass_mark}</b>
                            </div>
                          </p>
                        </div>

                        <div>
                          <p className="text-[var(--color-font-color)] ml-5">
                            <div>
                              {" "}
                              {t("Duration of Exam")} : <b>{item.duration}</b>
                            </div>
                          </p>
                        </div>

                        <div>
                          <p className="text-[var(--color-font-color)]">
                            <div>
                              {t("Attended on")} :{" "}
                              <b>
                                {new Date(item.passed_date).toLocaleDateString(
                                  "en-US"
                                )}
                              </b>
                            </div>
                          </p>
                        </div>
                      </div>
                    </Card>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card className="bg-pink-50 shadow-xl rounded-lg border-none">
                <CardContent className="p-2 flex flex-col justify-center items-center text-center text-[var(--color-font-color)]">
                  <span> {t("No passed exams found")}</span>
                  <Separator className="my-2 w-full border border-slate-300" />
                  <p> {t("Passed Exams will be listed here")}</p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
      <div className="flex justify-end mt-4">
        <Button variant="outline" className="rounded-md" onClick={handleCancel}>
          {t("Back")}
        </Button>
      </div>
    </MainLayout>
  );
}
