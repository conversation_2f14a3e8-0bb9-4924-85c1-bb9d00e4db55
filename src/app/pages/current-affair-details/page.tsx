"use client";
import React, { useEffect, useState } from "react";
import "../../../styles/main.css";
import MainLayout from "../layouts/mainLayout";
import { Button } from "@/components/ui/button";
import { FileText } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { CURRENT_AFFAIR_KEY, getLocalStorageItem } from "@/lib/utils";
import { KEYS } from "@/lib/keys";
import NextBreadcrumb from "@/components/breadcrumb";
import { InnerItem } from "@/types";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { useTranslation } from "next-i18next";
export default function CurrentAffairDetails(): React.JSX.Element {
  const { t } = useTranslation("common");
  const searchParams = useSearchParams();
  const title = searchParams?.get("title") ?? "";
  const data = getLocalStorageItem(KEYS.CURRENT_AFFAIR_CONSTANT);
  const details = data ? JSON.parse(data) : {};
  const router = useRouter();
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      month: "long",
      day: "numeric",
    };
    const formattedDate = date.toLocaleDateString(undefined, options);

    return formattedDate;
  };
  const handleCancel = () => {
    router.push(`/pages/current-affairs`);
  };
  const formattedDate = formatDate(title);

  useEffect(() => {
    setBreadcrumbItems(getBreadCrumbItems("Current Affairs Details", {}));
  }, []);
  return (
    <MainLayout titleText={""}>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="flex flex-col pt-2">
        <div className="flex flex-row">
          <FileText className="common-orange mr-4" />
          <div className="font-semibold text-base">{details.title}</div>
        </div>
        <div>
          <div
            className=" pt-2"
            dangerouslySetInnerHTML={{ __html: details.content }}
          ></div>
        </div>
        <div className="flex justify-end pt-5"></div>
      </div>
      <div className="flex justify-end mt-4 sticky bottom-2">
        <Button
          variant="outline"
          className="rounded-md "
          onClick={handleCancel}
        >
          {t("Back")}
        </Button>
      </div>
    </MainLayout>
  );
}
