"use client";
import * as React from "react";
import { Carousel } from "react-responsive-carousel";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import MainLayout from "../layouts/mainLayout";
import { Card, CardHeader, CardTitle } from "@/components/ui/card";
import "../../../styles/main.css";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useCourse } from "@/hooks/useCourse";
import { useTranslation } from "next-i18next";
import {
  AllResourceResponse,
  CourseDetailsResultType,
  InnerItem,
} from "@/types";
import { useMediaQuery } from "react-responsive";
import { Atom, CircleHelp } from "lucide-react";
import Image from "next/image";
import { Spinner } from "@/components/ui/progressiveLoder";
import { ERROR_MESSAGES } from "@/lib/messages";
import { KEYS } from "@/lib/keys";
import { getLocalStorageItem } from "@/lib/utils";
import ViewResources from "../dashboard/viewResource";
import ResourcesList from "../dashboard/resources";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { Modal } from "@/components/ui/modal";
import ViewAllResourceModal from "./viewAllResourceModal";
import { Button } from "@/components/ui/button";
// import ViewResources from "./viewResource";

export default function CourseDetails() {
  const { t } = useTranslation("common");
  const [currentSlide, setCurrentSlide] = React.useState(0);
  const [isLoading, setIsLoading] = React.useState(true);
  const [viewAllSubjects, setViewAllSubjects] = React.useState(false);
  const [showExam, setShowExam] = React.useState(false);
  const [viewAllGeneralResource, setViewAllGeneralResource] =
    React.useState(false);
  const [courseName, setCourseName] = React.useState<string>("");
  const handleSlideChange = (index: number) => {
    setCurrentSlide(index);
  };
  const [courseData, setCourseData] = React.useState<CourseDetailsResultType>();
  const router = useRouter();
  const courseID = getLocalStorageItem(KEYS.COURSE_ID);
  const orgID = getLocalStorageItem(KEYS.ORG_ID);
  const [breadcrumbItems, setBreadcrumbItems] = React.useState<InnerItem[]>([]);

  const goToExams = (): void => {
    router.push(`/pages/exams-list?course_id=${courseID}`);
  };
  const goToSubjects = (section_id: string): void => {
    // router.push(
    //   `/pages/subject-details?section_id=${section_id}&course_id=${courseID}`
    // );
    router.push(
      `/pages/course-resource?section_id=${section_id}&course_id=${courseID}`
    );
  };
  const [subjectDisplayCount, setSubjectDisplayCount] =
    React.useState<number>(0);
  const [limitedGeneralResources, setLimitedGeneralResources] = React.useState<
    AllResourceResponse[]
  >([]);
  const [AllVideoResources, setAllVideoResources] = React.useState<
    AllResourceResponse[]
  >([]);
  const { courseDetails } = useCourse();
  useEffect(() => {
    const configData = JSON.parse(
      localStorage.getItem("configurations") || "{}"
    );
    setShowExam(configData?.exams?.show_exam === 1 ? true : false);
    const displayCount = configData?.general_resources?.no_of_items;
    setSubjectDisplayCount(configData?.subjects?.no_of_items || 0);

    const limitedResources = configData?.general_resources?.resources?.slice(
      0,
      displayCount
    );
    limitedResources?.forEach((item: any) => {
      item.course_id = courseID;
    });

    setLimitedGeneralResources(limitedResources);
    setAllVideoResources(configData?.general_resources?.resources || []);
    setBreadcrumbItems(
      getBreadCrumbItems("Course Details", {
        course_id: courseID as string,
      })
    );
    let course = getLocalStorageItem(KEYS.SELECTED_COURSE);
    setCourseName(course as string);
    getCourseDetails();
    localStorage.removeItem(KEYS.VISITED_CHECKPOINTS);
    localStorage.removeItem(KEYS.CURRENT_SLIDE);
  }, []);

  const getCourseDetails = async (): Promise<void> => {
    try {
      const response = await courseDetails({
        org_id: orgID as string,
        course_id: courseID as string,
      });
      setIsLoading(false);
      setCourseData(response[0]);
    } catch (error) {}
  };
  const closeDialog = (): void => {
    setViewAllGeneralResource(false);
    setViewAllSubjects(false);
  };
  
  console.log("AllVideoResources", AllVideoResources);
  console.log("limitedGeneralResources", limitedGeneralResources);

  return (
    <MainLayout titleText="" showMenuBar={true}>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      {isLoading ? (
        <div className="flex justify-center items-center h-screen">
          <Spinner></Spinner>
        </div>
      ) : (
        <div className="">
          <h1 className="text-2xl font-bold">
            {t("Course")} - {courseData?.full_name}
          </h1>
          <div>
            <div className="mt-8" id="video-carosel">
              <div
                className="text-left text-24 font-bold mb-4 flex justify-between items-center"
                style={{ fontSize: 24 }}
              >
                <span className="text-2xl text-[var(--color-font-color)] font-bold flex items-center">
                  {t("General Resources")}
                  {AllVideoResources?.length >
                    limitedGeneralResources?.length && (
                    <button
                      className="text-[var(--color-nav-text)] font-semibold underline text-base ml-2 mt-1 hover:text-[var(--color-nav-hover-text)]"
                      onClick={() => setViewAllGeneralResource(true)}
                    >
                      ({t("View All")})
                    </button>
                  )}
                </span>
              </div>
              <ViewResources
                resources={limitedGeneralResources}
                title=""
                isModal={false}
                hideProgress={true}
                isGeneralResource={true}
              />
            </div>
            <div className="mt-8" id="video-carosel">
              <div
                className="text-left text-24 font-bold mb-2 flex justify-between items-center"
                style={{ fontSize: 24 }}
              >
                {/* <span>Assignments</span> */}
                {/* {resourceDisplayCount > 1 && (
                  <button
                  className="text-blue-500 underline text-sm"
                  onClick={() => setViewAllResource(true)}
                  >
                  View All
                  </button>
                )} */}
              </div>
              <ResourcesList courseID={courseID as string} hideExam={true} />
            </div>
            <div className="mt-8 w-full">
              <div
                className="text-left text-24  mb-4 flex flex-col"
                style={{ fontSize: 24 }}
              >
                <span className="text-2xl text-[var(--color-font-color)] font-bold flex items-center">
                  {t("Subjects")}
                  {(courseData?.sections ?? []).length >
                    subjectDisplayCount && (
                    <button
                      className="text-[var(--color-nav-text)] font-semibold underline text-base ml-2 mt-1 hover:text-[var(--color-nav-hover-text)]"
                      onClick={() => setViewAllSubjects(true)}
                    >
                      ( {t("View All")})
                    </button>
                  )}
                </span>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
                {courseData?.sections
                  .slice(0, subjectDisplayCount) // Slice the array to show only the first 'subjectDisplayCount' items
                  .map((item: any, index) => (
                    <Card
                      className="common-bg-color cursor-pointer h-full w-full"
                      key={item.id}
                      onClick={() => goToSubjects(item.section_id)}
                    >
                      <div className="flex flex-col h-full">
                        <CardHeader className="flex items-center justify-center flex-1">
                          <Atom
                            className="text-white"
                            style={{ width: "3rem", height: "3rem" }}
                          />
                        </CardHeader>
                        <CardTitle
                          className="text-white text-center text-base p-4 w-full truncate"
                          title={item.name}
                        >
                          {item.name}
                        </CardTitle>
                      </div>
                    </Card>
                  ))}
              </div>
            </div>

            {showExam && (
              <div className="mt-8 ">
                <div className="text-2xl text-[var(--color-font-color)] font-bold mb-2">
                  {t("Exams")}
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
                  <Card
                    className="common-yellow-bg cursor-pointer h-full w-full"
                    onClick={() => goToExams()}
                  >
                    <div className="flex flex-col h-full">
                      <CardHeader className="flex items-center justify-center flex-1">
                        <CircleHelp
                          className="text-white"
                          style={{ width: "3rem", height: "3rem" }}
                        />
                      </CardHeader>
                      <CardTitle className="text-white text-center text-base p-4 w-full truncate">
                        <p> {t("New Exams")}</p>
                      </CardTitle>
                    </div>
                  </Card>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {viewAllGeneralResource && (
        <Modal
          title="General Resources"
          header=""
          openDialog={viewAllGeneralResource}
          closeDialog={closeDialog}
          type="max-w-3xl"
        >
          <ViewAllResourceModal
            closeDialog={closeDialog}
            isGeneralResource={true}
            AllVideoResources={AllVideoResources}
            CourseId={courseID}
          />
        </Modal>
      )}
      {viewAllSubjects && (
        <Modal
          title={t("Subjects")}
          header=""
          openDialog={viewAllSubjects}
          closeDialog={closeDialog}
          type="max-w-3xl"
        >
          <div className=" w-full">
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4">
              {courseData?.sections
                // Slice the array to show only the first 'subjectDisplayCount' items
                .map((item: any, index) => (
                  <Card
                    className="common-bg-color cursor-pointer h-full w-full"
                    key={item.id}
                    onClick={() => goToSubjects(item.section_id)}
                  >
                    <div className="flex flex-col h-full">
                      <CardHeader className="flex items-center justify-center flex-1">
                        <Atom
                          className="text-white"
                          style={{ width: "3rem", height: "3rem" }}
                        />
                      </CardHeader>
                      <CardTitle
                        className="text-white text-center text-base p-4 w-full truncate"
                        title={item.name}
                      >
                        {item.name}
                      </CardTitle>
                    </div>
                    <div className="flex justify-end mt-4"></div>
                  </Card>
                ))}
            </div>
            <div className="flex justify-end mt-4">
              <Button variant="outline" className="" onClick={closeDialog}>
                {t("Close")}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </MainLayout>
  );
}
