"use client";
import React from "react";
// import "../../../styles/main.css";
import { welcomeData } from "@/lib/constants";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useTranslation } from "next-i18next";

export default function EmptyCourse(): React.JSX.Element {
  const router = useRouter();

  const backToLogin = () => {
    router.push("/pages/login");
  };
  const { t } = useTranslation("common");
  return (
    <div className="pt-[15vh]  bg-[#00B9C6]  h-screen pl-10 pr-10">
      <div className="flex flex-col items-center ">
        <div>
          <Image
            src="/assets/app_icon.png"
            alt="Profile Image"
            width={174.93}
            height={139}
            className=""
          />
        </div>
        <div className="font-semibold text-2xl text-white">
          {welcomeData.title}
        </div>
        <div className="font-normal text-base text-white">
          {welcomeData.noCourseMsg}
        </div>
      </div>

      <div className="grid md:justify-center lg:justify-center pt-10">
        <Button
          type="submit"
          className="space-x-2 rounded-lg md:w-96 h-12 lg:w-96 sm:w-full "
          variant="default"
          onClick={backToLogin}
        >
          <span className="text-white text-base font-semibold">
            {t("Back to Login")}
          </span>
        </Button>
      </div>
    </div>
  );
}
