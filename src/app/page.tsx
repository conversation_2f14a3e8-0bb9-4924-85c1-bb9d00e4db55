"use client";

import React from "react";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import FcmTokenComp from '../components/message/firebaseForeground.js';

export default function Home(): React.JSX.Element {
  const router = useRouter();

  useEffect(() => {
    const userId = localStorage.getItem("userId");

    if (userId) {
      router.push("pages/dashboard"); // Redirect to the dashboard if userId exists
    } else {
      router.push("pages/login"); // Redirect to the login page otherwise
    }
  }, []);

  return <div> <FcmTokenComp /></div>;
}