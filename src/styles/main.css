.carousel .slide iframe {
  margin: 0px !important;
  display: block;
}
/* #video-carosel 
  .carousel .control-dots {
    width: 50%;
  }
  #video-carosel .carousel{
    width: 50%;
  } */
/* #video-carosel {
  width: 50%;
} */
.color-orange {
  color: #fdb666;
}
/* @media (max-width: 900px) {
  #video-carosel {
    width: 100%;
  }
} */
.card {
  width: 100%; /* Default width */
  height: 130px; /* Default height */
  background-color: #00c6d4;
}

/* Media query for screens greater than 900px */
@media (min-width: 900px) {
  .card {
    max-width: 160px; /* Adjusted width for larger screens */
    height: 130px; /* Height remains 130px */
  }
}
.common-blue-border {
  border-color: #00c6d4;
}
.common-bg-color {
  background-color: #00c6d4;
}
.common-yellow-bg {
  background-color: #fdb666;
}
.common-yellow-text {
  color: #fdb666;
}
.common-white {
  background-color: #ffffff;
}
.common-orange {
  color: #fb8500;
}
.common-orange-border {
  border: #FB8500;
}
.common-bg-color-orange {
background-color: #fb8500;
}
.exam-feedback-icon {
  color: #00afbb;
}
.exam-previous-btn {
  background-color: #423338;
}
.exam-next-btn {
  background-color: #155264;
}
.exam-finish-btn {
  background-color: #9fc089;
}
.right-badge {
  right: -20px;
}
.position-round {
  bottom: -10px;
  left: 48px;
}
.bottom-rank {
  bottom: 0px;
}
.right-badge {
  right: -20px;
}
.position-round {
  bottom: -10px;
  left: 48px;
}
.bottom-rank {
  bottom: 0px;
}
.exam-progress-pending {
  background-color: #ffc107;
}

.exam-progress-attended {
  background-color: #48c10c;
}

.exam-progress-review {
  background-color: #4c4e4d;
}
.text-profile{
  font-size: 13px;
  line-height: 15px;
}

.exam-progress-select {
  background-color: #01a4d4;
}
.text-destructive {
  /* font-size: 13px !important; */
  font-weight: normal !important;
}
.profile .border-b h3{
  background-color: #FB8500;
  min-height: 50px;
  padding-left: 1rem;
 
}
.profile .border-b h3{
color: white;
font-size: 15px;
font-weight: 400;
line-height: 22.5px;
 
}
.profile .border-b:first-child h3{
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.profile .border-b:last-child h3{
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
} 
.left-camera{
  left:6rem;
  top:6rem;
}
.profile .border-b .pb-4{
  padding-bottom: 0px !important;
}
.profile .border-b  .lucide-chevron-down{
  margin-right: 15px;
}
.data-\[state\=checked\]\:bg-primary[data-state=checked]{
  background-color: #00AFBB;
}
.btn-bg-green {
  background-color: #8AA54E;
}
.chart-bg-green {
  background-color: #00A642;
}

.chart-bg-red {
  background-color: #FF2B2B;
}

.chart-bg-yellow {
  background-color: #FFC107;
}

.mark-card-bg {
  background-color: #FDB666;
}

.file-search-color {
  color: #00B9C7
}

.correct-mark-color {
  color: #00A642;
}

.wrong-mark-color {
  color: #FF2A2A;
}

.skipped-mark-color {
  color: #FFC107;
}
.answer-border {
  margin-top:-17px
}
.emoji p img{
  display: inline !important;
}
@keyframes fadeIn {
  from { 
    opacity: 0;
    transform: translateY(10px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.8s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.8s ease-out;
}

.hover:animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@media (min-width: 700px) and (max-width: 1400px) {
  .nexthub-grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.custom-player .react-player__preview {
  background-size: contain !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
  width: 100% !important;
  height: 100% !important;
}
