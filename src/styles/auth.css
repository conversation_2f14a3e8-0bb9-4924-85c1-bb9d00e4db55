/* LoginAccount.css */
.background {
    background-color:#218faa; 
    height: 100%; 
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .min-h-screen{
    /* background-color:#fff; */
  }
  
  .container {
    padding: 20px; 
    border-radius: 5px; 
    /* box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);  */
  }
  .login-container{
    background-color: #fff;
    padding: 10px;
    border-radius: 5px;
    min-height: 350px;
  }
  .login-container p a{
    color: #218faa;
  }
  .login-container button{
    /* background-color: #ffa500; */
    border-radius: 5px;
    width: 330px !important;
  }

  .azure-container buton {
    background-color: #fff;
  }

  .login-container input{
    border-radius: 8px;
  }
    
    .card-container {
      width: 100%;
      margin: auto;
      background-color: #fff;
      max-width: 32rem;
    }
    .bg-image {
      background-image: url('../../public/assets/LMS-bgImage.PNG'); 
      background-size: cover;
      background-position: center center;
      
    }
    .card-image{
      display: flex; 
      justify-content: center;
      align-items: center;
      
    }
  
    .card-header {
      margin-top: 0.25rem;
      text-align: center;
      font-size: 1.5rem;
    }
    
    .card-content {
      display: grid;
      gap: 1rem;
    }
    
    .input-container {
      gap: 0.5rem;
    }
    
    .next-button {
      color: red; 
    }
    
  
    .card-footer {
      display: flex;
      justify-content: space-between;
    }
  
    .login-div{
      padding: 6rem;
    }
    .lg:max-w-none {
      max-width: none;
  }
  
  .logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff; /* Background color of the square */
    border-radius: 80%; /* Border radius to make it a rounded square */
    overflow: hidden; /* Hide overflowing parts of the circle */
   
  }
  
  .circle-background {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%; /* Make it a circle */
   background-color: #fff; /* Background color of the circle */
  }
  
  .logo-image {
    border-radius: 80%; /* Make the image itself a circle */
    max-width: 100%;
    max-height: 100%;
  }
  .label-container {
    margin-left: 16px; /* Adjust the spacing between the image and the label */
  }
  
  .label-title {
    font-size: 1.25rem; /* Adjust the font size for the title */
    font-weight: bold;
  }
  
  .label-description {
    font-size: 1rem; /* Adjust the font size for the description */
    color: #fff; /* Adjust the color as needed */
  }
  .navigation-link-color {
    color: #218faa;
  }