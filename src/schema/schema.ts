import { z } from "zod";
export const LoginFormSchema = z.object({
  email: z
    .string({
      required_error: "Please enter your username to sign in",
    })
    .min(8, "Username must be at least 8 characters long. Please try again."),
  password: z
    .string({
      required_error: "Please enter your password",
    })
    .min(4, "Password must be at least 4 characters long"),
});
export const ForgotPasswordFormSchema = z.object({
  email: z.string().email({ message: "Invalid email format" }),
});
export const SignUpFormSchema = z
  .object({
    firstName: z
      .string({
        required_error: "Please enter first name",
      })
      .min(2, "First name must be atleast 2 characters long"),
    lastName: z
      .string({
        required_error: "Please enter last name",
      })
      .min(1, "Last name must be atleast 1 characters long"),
    email: z
      .string({
        required_error: "Please enter email",
      })
      .email({ message: "Invalid email format" }),
    password: z
      .string({
        required_error: "Please enter password",
      })
      .min(8, "Password must be atleast 8 characters long")
      .regex(
        /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&#+])[A-Za-z\d@$!%*?&#+]{8,}$/,
        "Password must include at least one uppercase letter, one lowercase letter, one number, and one special character."
      ),
    confirmPassword: z
      .string({
        required_error: "Please enter confirm password",
      })
      .min(8, "Password must be atleast 8 characters long")
      .regex(
        /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&#+])[A-Za-z\d@$!%*?&#+]{8,}$/,
        "Password must include at least one uppercase letter, one lowercase letter, one number, and one special character."
      ),
    phoneNumber: z
      .string({
        required_error: "Please enter phone number",
      })
      .min(10, "Phone Number must be atleast 10 characters long"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });
export const ProfileFormSchema = z.object({
  firstName: z
    .string({
      required_error: "Please enter first name",
    })
    .min(2, "First name must be at least 2 characters"),

  lastName: z
    .string({
      required_error: "Please enter last name",
    })
    .min(2, "Last name must be at least 2 characters"),

  email: z
    .string({
      required_error: "Please enter email id",
    })
    .min(5, "Email must be at least 5 characters long")
    .email("Please enter a valid email address"),

    phoneNo: z.string({
      required_error: "Please enter phone number",
    })
    .min(10, "Phone Number must be at least 10 characters long"),
});

export const OrganizationFormSchema = z.object({
  organization: z
    .array(
      z.object({
        org_id: z.string(),
        org_name: z.string(),
      })
    )
    .refine((value) => value.length > 0, {
      message: "You have to select at least one item.",
    }),
});
