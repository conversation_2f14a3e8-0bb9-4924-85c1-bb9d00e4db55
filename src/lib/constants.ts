import React from "react";
import ReactDOMServer from "react-dom/server";
import { useTranslation } from "next-i18next";
import i18next from "i18next";
import {
  LayoutDashboardIcon,
  UserIcon,
  FileText,
  CirclePlay,
  CircleHelp,
  Folder,
  File,
  NotebookText,
  Video,
  FileQuestion,
  Files,
  Settings,
  BarChartBig,
  Newspaper,
  Bell,
  BookOpen,
  MessageSquareText,
  Repeat2,
  LogOut,
  HomeIcon,
  FolderOpen,
  CornerDownRight,
  SubscriptIcon,
  CalendarPlus,
} from "lucide-react";
import { Description } from "@radix-ui/react-toast";
import { subtle } from "crypto";
import { KEYS } from "./keys";

export const sidemenu = [
  {
    name: "Home",
    href: "/pages/dashboard",
    icon: HomeIcon,
    color: "white",
  },
  {
    name: "Profile",
    href: "/pages/profile",
    icon: UserIcon,
    color: "white",
  },
  // {
  //   name: "<PERSON><PERSON><PERSON>",
  //   href: "/",
  //   icon: Settings,
  //   color: "white",
  // },
  {
    name: "Ranking",
    href: "/pages/course-rank-list",
    icon: BarChart<PERSON>ig,
    color: "white",
  },
  // {
  //   name: "News",
  //   href: "/",
  //   icon: Newspaper,
  //   color: "white",
  // },
  {
    name: "Subscriptions",
    href: "/pages/subscriptions",
    icon: CalendarPlus,
    color: "white",
  },
  {
    name: "Join Meeting",
    href: "/pages/join-meeting",
    icon: Video,
    color: "white",
  },
  // {
  //   name: "Notification",
  //   href: "/",
  //   icon: Bell,
  //   color: "white",
  // },
  // {
  //   name: "About",
  //   href: "/",
  //   icon: BookOpen,
  //   color: "white",
  // },
  // {
  //   name: "Feedback",
  //   href: "/",
  //   icon: MessageSquareText,
  //   color: "white",
  // },
  // {
  //   name: "Share",
  //   href: "/",
  //   icon: Repeat2,
  //   color: "white",
  // },
  {
    name: "Signout",
    href: "",
    icon: LogOut,
    color: "white",
  },
];
export const currentAffairsImages = [
  "/images/current_affairs_default.png",
  "/images/current_affairs_default.png",
  "/images/current_affairs_default.png",
  "/images/current_affairs_default.png",
  "/images/current_affairs_default.png",
  "/images/current_affairs_default.png",
];
export const examNames = [
  "LDC Practice Exam",
  "General Knowledge",
  "General GK English",
];
export const videoUrls = [
  "https://youtu.be/QVNpTiFsGvw",
  "https://www.youtube.com/watch?v=uXWycyeTeCs",
  "https://www.youtube.com/watch?v=uXWycyeTeCs",
];

export const courseResourceData = [
  {
    name: "Micro Organisms",
    type: "File",
    file: "https://devlqc.citrusdev.com/assets/images/kitten-default.png",
  },
  {
    name: "Micro Organisms Evolution",
    type: "File",
    file: "https://devlqc.citrusdev.com/assets/images/camera4.jpg",
  },
  {
    name: "Micro Organisms Exam2",
    type: "Quiz",
    file: "",
  },
  {
    name: "Micro Organisms Exam1",
    type: "Quiz",
    file: "",
  },
  {
    name: "Micro Organisms Video1",
    type: "Video",
    file: "https://youtu.be/9JW63U2mzqo?si=jtBgWmlP6GyrbssM",
  },

  {
    name: "Micro Organisms Video2",
    type: "Video",
    file: "https://youtu.be/JZjzQhFG6Ec?si=FKOiogixDMBTwfvD",
  },
  {
    name: "Micro Organisms PDF1",
    type: "PDF",
    file: "https://devlqc.citrusdev.com/assetsfiles/sample.pdf",
  },

  {
    name: "Micro Organisms Document1",
    type: "Document",
    file: "https://devlqc.citrusdev.com/files/sample-ppt-file.ppt",
  },
  {
    name: "Micro Organisms Document1",
    type: "Document",
    file: "https://devlqc.citrusdev.com/files/Guidelines-for-Providing-the-Developer.docx",
  },

  {
    name: "Micro Organisms Document1",
    type: "Document",
    file: "https://devlqc.citrusdev.com/files/public_nih.xlsx",
  },

  {
    name: "Micro Organisms Page1",
    type: "Page",
    file: "",
  },
  {
    name: "Micro Organisms Page1",
    type: "Page",
    file: "",
  },
];

export const folderData = [
  {
    file: "New folder1",
    icon: Folder,
  },
  {
    file: "New folder2",
    icon: Folder,
  },
  {
    file: "New folder3",
    icon: Folder,
  },
];
export const examListData = [
  {
    examName: "GK Test Exam",
    questionsNo: "4 Questions",
    totalmarks: "20 marks",
    passmark: "15 passmark",
    time: "20 min",
  },
  {
    examName: "Maths Test Exam",
    questionsNo: "4 Questions",
    totalmarks: "20 marks",
    passmark: "15 passmark",
    time: "20 min",
  },
  {
    examName: "English Test Exam",
    questionsNo: "4 Questions",
    totalmarks: "20 marks",
    passmark: "15 passmark",
    time: "20 min",
  },
  {
    examName: "Subjective Exam",
    questionsNo: "4 Questions",
    totalmarks: "20 marks",
    passmark: "15 passmark",
    time: "20 min",
  },
];
export const examsIntroData = {
  headingTeaxt: "LDC",
  sectionSubTeaxt1: "Duration",
  sectionSubPara1: "Attend 10 question within 25 minutes",
  sectionSubTeaxt2: "Mark",
  sectionSubPara2: "20.0 is the total mark and 15.0 is the pass mark",
  sectionSubTeaxt3: "Exam Available in",
  sectionSubPara3: "From 10th Aug 2023 to 9th Oct 2023",
  sectionSubTeaxt4: "Exam Tips",
  sectionSubPara4: "Rules to follow while attending exam:",
  sectionPragraph:
    "1.Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dumm text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.",
};

export const resourceType = [
  {
    name: "File",
    icon: File,
  },
  {
    name: "Page",
    icon: NotebookText,
  },
  {
    name: "Video",
    icon: Video,
  },
  {
    name: "Quiz",
    icon: FileQuestion,
  },
  {
    name: "PDF",
    icon: FileText,
  },
  {
    name: "Document",
    icon: Files,
  },
];

export const examAnsweredViewData = [
  {
    imgURL: "/images/girl.png",
    name: "Misha muraly",
    correct: 12,
    wrong: 4,
    skipped: 1,
    totalMark: "32.5",
  },
  {
    imgURL: "/images/girl.png",
    name: "Misha muraly",
    correct: 12,
    wrong: 4,
    skipped: 1,
    totalMark: "32.5",
  },
  {
    imgURL: "/images/girl.png",
    name: "Misha muraly",
    correct: 12,
    wrong: 4,
    skipped: 1,
    totalMark: "32.5",
  },
];
export const examAnsweredView = [
  {
    imgURL: "/images/girl_square.jpg",
    name: "Misha muraly",
    correct: 12,
    wrong: 4,
    skipped: 1,
    totalMark: "32.5",
    badge_url: "/assets/badge_first.png",
  },
  {
    imgURL: "/images/girl_square.jpg",
    name: "Misha muraly",
    correct: 12,
    wrong: 4,
    skipped: 1,
    totalMark: "32.5",
    badge_url: "/assets/badge_second.png",
  },
  {
    imgURL: "/images/girl_square.jpg",
    name: "Misha muraly",
    correct: 12,
    wrong: 4,
    skipped: 1,
    totalMark: "32.5",
    badge_url: "/assets/badge_third.png",
  },
];
export const questionData = [
  {
    question:
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry?",
    options: [
      "It has survived not only five centuries, but also the leap into electronic typesetting .",
      "It has survived not only five centuries, but also the leap into electronic typesetting.",
      "It has survived not only five centuries, but also the leap into electronic typesetting.",
      "It has survived not only five centuries, but also the leap into electronic typesetting.",
      "None of the above",
    ],
    isFlagged: false,
  },
  {
    question: "What is the capital city of France?",
    options: ["Paris .", " Berlin.", "London.", "Madrid.", "None of the above"],
    isFlagged: false,
  },
  {
    question: "Which planet is known as the Red Planet?",
    options: ["Earth", "Mars", "Jupiter", "Venus", "None of the above"],
    isFlagged: false,
  },
  {
    question: "Who wrote the play 'Romeo and Juliet'?",
    options: [
      "William Shakespeare",
      "Charles Dickens",
      "J.K. Rowling",
      "Mark Twain",
      "None of the above",
    ],
    isFlagged: false,
  },
  {
    question: "What is the largest ocean on Earth?",
    options: [
      "Atlantic Ocean",
      "Indian Ocean",
      "Arctic Ocean",
      "Pacific Ocean",
      "None of the above",
    ],
    isFlagged: false,
  },
  {
    question: "What is the chemical symbol for gold?",
    options: ["Au", "Ag", "Pb", "Fe", "None of the above"],
    isFlagged: false,
  },

  {
    question: "What is the square root of 64?",
    options: ["6", "7", "8", "9", "None of the above"],
    isFlagged: false,
  },
  {
    question: "Who is the current President of the United States? (as of 2024)",
    options: [
      "Joe Biden",
      "Donald Trump",
      "Barack Obama",
      "Kamala Harris",
      "None of the above",
    ],
    isFlagged: false,
  },
  {
    question: "What is the smallest prime number?",
    options: ["0", "1", "2", "3", "None of the above"],
    isFlagged: false,
  },
  {
    question: "What is the speed of light?",
    options: [
      "3.0 x 10^8 m/s",
      "3.0 x 10^7 m/s",
      "3.0 x 10^6 m/s",
      "3.0 x 10^5 m/s",
      "None of the above",
    ],
    isFlagged: false,
  },
  {
    question:
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry?",
    options: [
      "It has survived not only five centuries, but also the leap into electronic typesetting .",
      "It has survived not only five centuries, but also the leap into electronic typesetting.",
      "It has survived not only five centuries, but also the leap into electronic typesetting.",
      "It has survived not only five centuries, but also the leap into electronic typesetting.",
      "None of the above",
    ],
    isFlagged: false,
  },
  {
    question: "What is the capital city of France?",
    options: ["Paris .", " Berlin.", "London.", "Madrid.", "None of the above"],
    isFlagged: false,
  },
  {
    question: "Which planet is known as the Red Planet?",
    options: ["Earth", "Mars", "Jupiter", "Venus", "None of the above"],
    isFlagged: false,
  },
  {
    question: "Who wrote the play 'Romeo and Juliet'?",
    options: [
      "William Shakespeare",
      "Charles Dickens",
      "J.K. Rowling",
      "Mark Twain",
      "None of the above",
    ],
    isFlagged: false,
  },
  {
    question: "What is the largest ocean on Earth?",
    options: [
      "Atlantic Ocean",
      "Indian Ocean",
      "Arctic Ocean",
      "Pacific Ocean",
      "None of the above",
    ],
    isFlagged: false,
  },
  {
    question: "What is the chemical symbol for gold?",
    options: ["Au", "Ag", "Pb", "Fe", "None of the above"],
    isFlagged: false,
  },
  {
    question: "What is the square root of 64?",
    options: ["6", "7", "8", "9", "None of the above"],
    isFlagged: false,
  },
  {
    question: "Who is the current President of the United States? (as of 2024)",
    options: [
      "Joe Biden",
      "Donald Trump",
      "Barack Obama",
      "Kamala Harris",
      "None of the above",
    ],
    isFlagged: false,
  },
  {
    question: "What is the smallest prime number?",
    options: ["0", "1", "2", "3", "None of the above"],
    isFlagged: false,
  },
  {
    question: "What is the speed of light?",
    options: [
      "3.0 x 10^8 m/s",
      "3.0 x 10^7 m/s",
      "3.0 x 10^6 m/s",
      "3.0 x 10^5 m/s",
      "None of the above",
    ],
    isFlagged: false,
  },
];
export const examreviewQuestions = [
  {
    question: "Who is the father of HTML?",
    answers: " NA",
  },
  {
    question: "Who is the father of HTML?",
    answers: " NA",
  },
  {
    question: "Who is the father of HTML?",
    answers: " NA",
  },
  {
    question: "Who is the father of HTML?",
    answers: " NA",
  },
  {
    question: "Who is the father of HTML?",
    answers: " NA",
  },
];

export const resourcePayment = [
  { name: "Demo 1", payment: "Weekly", price: 30 },
  { name: "Demo 2", payment: "Monthly", price: 40 },
  { name: "Demo 3", payment: "Yearly", price: 50 },
  { name: "Demo 4", payment: "Monthly", price: 45 },
  { name: "Demo 5", payment: "Yearly", price: 55 },
  { name: "Demo 6", payment: "Weekly", price: 35 },
  { name: "Demo 7", payment: "Monthly", price: 65 },
  { name: "Demo 8", payment: "Yearly", price: 60 },
  { name: "Demo 9", payment: "Weekly", price: 38 },
];

export const cardColor = [
  { bg: "#E0FFDE", border: "#C1ECBF" },
  { bg: "#FEFAEE", border: "#F8DAA9" },
  { bg: "#D4EEFD", border: "#ABDBFE" },
  { bg: "#e4fee4", border: "#c0ebc2" },
  { bg: "#fffbf1", border: "#fad9ad" },
  { bg: "#d8f0fd", border: "#a8dcfc" },
  { bg: "#d0fdfe", border: "#acddde" },
  { bg: "#e1fff0", border: "#caf1de" },
  { bg: "#fff6eb", border: "#ffe7c7" },
];

export const subscriptionDummyData = [
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
  { name: "Dummy data 1" },
];

export const subscriptionAlerts =
  "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Impedit quidem neque nihil odit unde voluptate, ab vero soluta obcaecati architecto voluptates nam suscipit, necessitatibus commodi quasi. Ullam odit animi nulla? Lorem ipsum dolor sit amet consectetur, adipisicing elit. Impedit quidem neque nihil odit unde voluptate, abvero soluta obcaecati architecto voluptates nam suscipit, necessitatibus commodi quasi. Ullam odit animi nulla?";

export const currentAffairsData = [
  {
    date: "3 Aug 2023",
    description:
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry.",
  },
  {
    date: "5 Aug 2023",
    description:
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry.",
  },
  {
    date: "13 Aug 2023",
    description:
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry.",
  },
  {
    date: "25 Aug 2023",
    description:
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry.",
  },
  {
    date: "28 Aug 2023",
    description:
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry.",
  },
];

export const topics = [
  {
    topic: "Science",
    icon: Folder,
    subtopics: [
      {
        icon: FolderOpen,
        subtopic: "Chemistry",
        courses: [
          { course: "Biochemistry", icon: CornerDownRight },
          { course: "Organic chemistry", icon: CornerDownRight },
          { course: "Polymer chemistry", icon: CornerDownRight },
        ],
      },
      {
        icon: FolderOpen,
        subtopic: "Biology",
        courses: [
          { course: "Biotechnology", icon: CornerDownRight },
          { course: "Microbiology", icon: CornerDownRight },
          { course: "Genetics", icon: CornerDownRight },
        ],
      },
      {
        icon: FolderOpen,
        subtopic: "Physics",
        courses: [
          { course: "Thermodynamics", icon: CornerDownRight },
          { course: "Nuclear physics", icon: CornerDownRight },
          { course: "Astrophysics", icon: CornerDownRight },
        ],
      },
      {
        icon: FolderOpen,
        subtopic: "Computer",
        courses: [
          { course: "Artificial intelligence", icon: CornerDownRight },
          { course: "Digital marketing", icon: CornerDownRight },
          { course: "Data science", icon: CornerDownRight },
        ],
      },
      {
        icon: FolderOpen,
        subtopic: "Mathematics",
        courses: [
          { course: "Differential equation", icon: CornerDownRight },
          { course: "Calculus", icon: CornerDownRight },
          { course: "Statistics", icon: CornerDownRight },
        ],
      },
    ],
  },
  {
    topic: "Medical science",
  },
  {
    topic: "Engineering",
  },
];
export const profile = {
  username: "Misha Muraly",
  mail: "<EMAIL>",
  mobileNo: "+91 **********",
};
export const purchaseData = [
  {
    title: "Purchased plans",
    icon: "/assets/purchased_plans.png",
    details: [
      // {
      //   subtitle: "Executive Plan Jan 2024",
      //   description:
      //     "Plan will be available from January 24, 2024 3:40PM to June 30, 2024 5:00PM",
      //   planamount: "$1000",
      // },
      // {
      //   subtitle: "Executive Plan Jan 2024",
      //   description:
      //     "Plan will be available from January 24, 2024 3:40PM to June 30, 2024 5:00PM",
      //   planamount: "$1000",
      // },
      // {
      //   subtitle: "Executive Plan Jan 2024",
      //   description:
      //     "Plan will be available from January 24, 2024 3:40PM to June 30, 2024 5:00PM",
      //   planamount: "$1000",
      // },
    ],
  },
  {
    title: "Plans waiting for approval",
    icon: "/assets/pending_plans.png",
    details: [
      // {
      //   subtitle: "Executive Plan Jan 2024",
      //   description:
      //     "Plan will be available from January 24, 2024 3:40PM to June 30, 2024 5:00PM",
      //   planamount: "$1000",
      // },
      // {
      //   subtitle: "Executive Plan Jan 2024",
      //   description:
      //     "Plan will be available from January 24, 2024 3:40PM to June 30, 2024 5:00PM",
      //   planamount: "$1000",
      // },
      // {
      //   subtitle: "Executive Plan Jan 2024",
      //   description:
      //     "Plan will be available from January 24, 2024 3:40PM to June 30, 2024 5:00PM",
      //   planamount: "$1000",
      // },
    ],
  },
];

export const resourceURL = "https://devlqc.citrusdev.com/files/";
export const PPT_SAMPLE_URL =
  "https://devlqc.citrusdev.com/files/sample-ppt-file.ppt";
export const DOCX_SAMPLE_URL =
  "https://devlqc.citrusdev.com/files/Guidelines-for-Providing-the-Developer.docx";
export const XLSX_SAMPLE_URL =
  "https://devlqc.citrusdev.com/files/public_nih.xlsx";

export const currentAffaisDetails = {
  title: "Why in news?",
  details:
    "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
};
export const DATE_FORMAT = "DD MMM YYYY";
export const welcomeData = {
  title: "Welcome to SmartLearn app",
  details:
    "Your SmartLearn account has been created! We are  Waiting for the admin to activate your account. You will receive an email once this is complete. For more details, please contact the <NAME_EMAIL>.",
  noCourseMsg:
    "Your SmartLearn account has been created! We are waiting for the admin to activate your account and enroll you in a course. You will receive an email once this is complete. For more details, please contact the <NAME_EMAIL>.",
};
export const reattendText =
  "Do not worry you can re-attend the exam from the home screen.";
export const BADGE_FIRST = "/assets/badge_first.png";
export const BADGE_SECOND = "/assets/badge_second.png";
export const BADGE_THIRD = "/assets/badge_third.png";
export const currencySymbols: any = {
  USD: "$",
  EUR: "€",
  INR: "₹",
  JPY: "¥",
  GBP: "£",
  AUD: "A$",
  CAD: "C$",
  CHF: "CHF",
  CNY: "¥",
  SEK: "kr",
  NZD: "NZ$",
  // Add more currencies as needed
};
export const PENDING_TEXT = "Pending";
export const APPROVE_TEXT = "Approved";
export const SELECT_TEXT = "Select";
export const PPT_Checkpoint = {
  status: "success",
  check_points: [
    {
      org_id: "884604a2-e7a2-4d18-98dd-684cb37d575c",
      sequence: 1,
      created_at: "2024-07-25T09:52:34.548707+00:00",
      created_by: "bd6fae1b-cb12-4b90-8fd4-f06f17ad76e9",
      start_page: null,
      slide_no: 5,
      updated_at: "2024-07-25T09:52:34.548707+00:00",
      updated_by: "bd6fae1b-cb12-4b90-8fd4-f06f17ad76e9",
      instance_id: "46720f4d-f3a6-40e7-ba95-4727efc2b952",
      module_name: "quiz",
      is_mandatory: true,
      checkpoint_id: "62e6a3e4-4993-4870-9564-70b0579f5a2d",
      module_type_id: "dd33330a-7ab4-452b-b8bd-9e37192f0ffb",
      checkpoint_name: "kerala History-cp 1",
      checkpoint_type: "Exam",
      course_module_id: "ba3ecd22-5513-4ce0-9cfd-c1ba4804d5a8",
      instance_end_time: "2024-12-31T10:34:00+00:00",
    },
    {
      org_id: "884604a2-e7a2-4d18-98dd-684cb37d575c",
      sequence: 2,
      created_at: "2024-07-25T09:52:34.548707+00:00",
      created_by: "bd6fae1b-cb12-4b90-8fd4-f06f17ad76e9",
      start_page: null,
      slide_no: 15,
      updated_at: "2024-07-25T09:52:34.548707+00:00",
      updated_by: "bd6fae1b-cb12-4b90-8fd4-f06f17ad76e9",
      instance_id: "0b70ca84-778a-4b2e-a41b-0e67fc868a6b",
      module_name: "quiz",
      is_mandatory: false,
      checkpoint_id: "ac9f05ac-85a3-48a2-9cb9-f0c8e2ce99bc",
      module_type_id: "dd33330a-7ab4-452b-b8bd-9e37192f0ffb",
      checkpoint_name: "Kerala History-cp 2",
      checkpoint_type: "Exam",
      course_module_id: "ba3ecd22-5513-4ce0-9cfd-c1ba4804d5a8",
      instance_end_time: "2024-12-31T10:34:00+00:00",
    },
  ],
};

export const Reset_PASSWORD_REDIRECT_URL =
  //"http://localhost:3000/reset-password";
  "https://competitor-admin-live.vercel.app/reset-password";

export function getFileType(filename: string, module_type: string) {
  // const { t } = useTranslation("common");
  const extension = filename?.toLowerCase();
  const imageExtensions = ["jpg", "jpeg", "png", "gif"];
  const documentExtensions = [
    "pdf",
    "doc",
    "docx",
    "ppt",
    "pptx",
    "xls",
    "xlsx",
  ];
  const videoExtensions = ["mp4", "avi", "mov", "mkv", "wmv", "flv", ".mp4"];
  if (module_type === "Quiz") {
    return "Quiz";
  } else {
    if (imageExtensions.includes(extension)) {
      return "Image";
    } else if (documentExtensions.includes(extension)) {
      if (extension === "pdf") {
        return "PDF Document";
      } else if (["doc", "docx"].includes(extension)) {
        return "Word Document";
      } else if (["ppt", "pptx"].includes(extension)) {
        return "PowerPoint Presentation";
      } else if (["xls", "xlsx"].includes(extension)) {
        return "Excel Spreadsheet";
      }
    } else if (videoExtensions.includes(extension)) {
      // return "Video";
      return i18next.t("Video");
    } else if (extension === "html") {
      return "Page";
    } else {
      return "Unknown file type";
    }
  }
}
export const mobResponsive = [
  {
    breakpoint: "1024px", // When screen width is less than or equal to 1024px
    numVisible: 1, // Show 1 item
    numScroll: 1, // Scroll 1 item at a time
  },
  {
    breakpoint: "768px", // When screen width is less than or equal to 768px
    numVisible: 1, // Show 1 item
    numScroll: 1, // Scroll 1 item at a time
  },
  {
    breakpoint: "640px", // When screen width is less than or equal to 640px
    numVisible: 1, // Show 1 item
    numScroll: 1, // Scroll 1 item at a time
  },
  {
    breakpoint: "480px", // When screen width is less than or equal to 480px
    numVisible: 1, // Show 1 item
    numScroll: 1, // Scroll 1 item at a time
  },
  {
    breakpoint: "320px", // Smallest screen size
    numVisible: 1, // Show 1 item
    numScroll: 1, // Scroll 1 item at a time
  },
];

export const COLORS = ["#0891b2", "#00C49F", "#FF8042", "#FFBB28", "#0088FE"];
export const ACQUIRED_MARKS = "Total Marks";
export const COVERED_PERCENTAGE = "Progress";
export const COVERED_TIME = "Time Spent";
export const ACHIEVEMENTS = "Achievements";
export const TOTAL_COURSE = "Total Course";

export const COMPLETED_COURSE = "Completed Course";
export const generateRandomColor = () => {
  const letters = "0123456789ABCDEF";
  let color = "#";
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

export const generateBarColors = (count: number) => {
  const colors = [];
  for (let i = 0; i < count; i++) {
    colors.push(generateRandomColor());
  }
  return colors;
};

export const ENROLLED_COURSES_SINGLE = "single";
export const ENROLLED_COURSES_MULTIPLE = "multiple";

export const stripHtmlTags = (inputString: string) => {
  return inputString.replace(/<[^>]*>/g, ""); // Regular expression to remove HTML tags
};
export const defaultOrgId = "87e018ab-149b-43e4-ab75-3633951015d1";
export const defaultRollId = "5628008b-ca03-4751-92f7-062bf5cfbceb";
export const defaultOrgName = "Competitor";
export const pptCheckpointAlert =
  "Complete the the Quiz to proceed to the next Topic.";
export const pptCompletionAlert =
  "Complete the resource to proceed to the next Topic.";
export const getPastYearMonths = (): { month: string; year: number }[] => {
  const monthsArray = [];
  const currentDate = new Date();

  for (let i = 0; i < 12; i++) {
    const pastDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() - i,
      1
    );
    const monthName = pastDate.toLocaleString("default", { month: "long" }); // Full month name
    const year = pastDate.getFullYear(); // Get correct year
    monthsArray.push({ month: monthName, year: year });
  }

  return monthsArray;
};

export const extractVideoIdFromSearch = (url: string): string | null => {
  try {
    // Check if the URL contains a "vid:" pattern inside fragments
    const vidMatch = url.match(/vid:([a-zA-Z0-9_-]{11})/);
    return vidMatch ? vidMatch[1] : null;
  } catch {
    return null;
  }
};
export const UUID = "00000000-0000-0000-0000-000000000000";
