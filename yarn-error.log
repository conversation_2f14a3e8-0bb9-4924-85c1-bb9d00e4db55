Arguments: 
  C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\bin\yarn.js add react-player react-responsive-carousel

PATH: 
  C:\ProgramData\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\TortoiseGit\bin;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk1.8.0_201\bin;C:\Python27;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Python27;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;

Yarn version: 
  1.22.17

Node version: 
  20.9.0

Platform: 
  win32 x64

Trace: 
  Error: EPERM: operation not permitted, unlink 'C:\citrus-works\LMS\smartlearn-web-app\node_modules\@next\swc-win32-x64-msvc\next-swc.win32-x64-msvc.node'

npm manifest: 
  {
    "name": "smartlearn-web",
    "version": "0.1.0",
    "private": true,
    "license": "MIT",
    "scripts": {
      "dev": "next dev",
      "build": "next build",
      "start": "next start",
      "lint": "next lint"
    },
    "dependencies": {
      "@hookform/resolvers": "^3.6.0",
      "@radix-ui/react-icons": "^1.3.0",
      "@radix-ui/react-label": "^2.1.0",
      "@radix-ui/react-menubar": "^1.1.1",
      "@radix-ui/react-slot": "^1.1.0",
      "@radix-ui/react-toast": "^1.2.1",
      "@typescript-eslint/eslint-plugin": "^7.14.1",
      "class-variance-authority": "^0.7.0",
      "clsx": "^2.1.1",
      "embla-carousel-react": "^8.1.6",
      "eslint-config-prettier": "^9.1.0",
      "eslint-plugin-prettier": "^5.1.3",
      "lucide-react": "^0.399.0",
      "next": "14.2.4",
      "react": "^18",
      "react-dom": "^18",
      "react-hook-form": "^7.52.0",
      "react-icons": "^5.2.1",
      "react-player": "^2.16.0",
      "react-responsive-carousel": "^3.2.23",
      "tailwind-merge": "^2.3.0",
      "tailwindcss-animate": "^1.0.7",
      "zod": "^3.23.8"
    },
    "devDependencies": {
      "@types/node": "^20",
      "@types/react": "^18",
      "@types/react-dom": "^18",
      "eslint": "^8",
      "eslint-config-next": "14.2.4",
      "husky": "^9.0.11",
      "postcss": "^8",
      "tailwindcss": "^3.4.4",
      "typescript": "5.1"
    }
  }

yarn manifest: 
  No manifest

Lockfile: 
  # THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
  # yarn lockfile v1
  
  
  "@alloc/quick-lru@^5.2.0":
    version "5.2.0"
    resolved "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz"
    integrity sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==
  
  "@babel/runtime@^7.24.1":
    version "7.24.7"
    resolved "https://registry.npmjs.org/@babel/runtime/-/runtime-7.24.7.tgz"
    integrity sha512-UwgBRMjJP+xv857DCngvqXI3Iq6J4v0wXmwc6sapg+zyhbwmQX67LUEFrkK5tbyJ30jGuG3ZvWpBiB9LCy1kWw==
    dependencies:
      regenerator-runtime "^0.14.0"
  
  "@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.4.0":
    version "4.4.0"
    resolved "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz"
    integrity sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==
    dependencies:
      eslint-visitor-keys "^3.3.0"
  
  "@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.6.1":
    version "4.11.0"
    resolved "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.11.0.tgz"
    integrity sha512-G/M/tIiMrTAxEWRfLfQJMmGNX28IxBg4PBz8XqQhqUHLFI6TL2htpIB1iQCj144V5ee/JaKyT9/WZ0MGZWfA7A==
  
  "@eslint/eslintrc@^2.1.4":
    version "2.1.4"
    resolved "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz"
    integrity sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==
    dependencies:
      ajv "^6.12.4"
      debug "^4.3.2"
      espree "^9.6.0"
      globals "^13.19.0"
      ignore "^5.2.0"
      import-fresh "^3.2.1"
      js-yaml "^4.1.0"
      minimatch "^3.1.2"
      strip-json-comments "^3.1.1"
  
  "@eslint/js@8.57.0":
    version "8.57.0"
    resolved "https://registry.npmjs.org/@eslint/js/-/js-8.57.0.tgz"
    integrity sha512-Ys+3g2TaW7gADOJzPt83SJtCDhMjndcDMFVQ/Tj9iA1BfJzFKD9mAUXT3OenpuPHbI6P/myECxRJrofUsDx/5g==
  
  "@floating-ui/core@^1.6.0":
    version "1.6.4"
    resolved "https://registry.npmjs.org/@floating-ui/core/-/core-1.6.4.tgz"
    integrity sha512-a4IowK4QkXl4SCWTGUR0INAfEOX3wtsYw3rKK5InQEHMGObkR8Xk44qYQD9P4r6HHw0iIfK6GUKECmY8sTkqRA==
    dependencies:
      "@floating-ui/utils" "^0.2.4"
  
  "@floating-ui/dom@^1.0.0":
    version "1.6.7"
    resolved "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.6.7.tgz"
    integrity sha512-wmVfPG5o2xnKDU4jx/m4w5qva9FWHcnZ8BvzEe90D/RpwsJaTAVYPEPdQ8sbr/N8zZTAHlZUTQdqg8ZUbzHmng==
    dependencies:
      "@floating-ui/core" "^1.6.0"
      "@floating-ui/utils" "^0.2.4"
  
  "@floating-ui/react-dom@^2.0.0":
    version "2.1.1"
    resolved "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.1.tgz"
    integrity sha512-4h84MJt3CHrtG18mGsXuLCHMrug49d7DFkU0RMIyshRveBeyV2hmV/pDaF2Uxtu8kgq5r46llp5E5FQiR0K2Yg==
    dependencies:
      "@floating-ui/dom" "^1.0.0"
  
  "@floating-ui/utils@^0.2.4":
    version "0.2.4"
    resolved "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.4.tgz"
    integrity sha512-dWO2pw8hhi+WrXq1YJy2yCuWoL20PddgGaqTgVe4cOS9Q6qklXCiA1tJEqX6BEwRNSCP84/afac9hd4MS+zEUA==
  
  "@hookform/resolvers@^3.6.0":
    version "3.6.0"
    resolved "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.6.0.tgz"
    integrity sha512-UBcpyOX3+RR+dNnqBd0lchXpoL8p4xC21XP8H6Meb8uve5Br1GCnmg0PcBoKKqPKgGu9GHQ/oygcmPrQhetwqw==
  
  "@humanwhocodes/config-array@^0.11.14":
    version "0.11.14"
    resolved "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.14.tgz"
    integrity sha512-3T8LkOmg45BV5FICb15QQMsyUSWrQ8AygVfC7ZG32zOalnqrilm018ZVCw0eapXux8FtA33q8PSRSstjee3jSg==
    dependencies:
      "@humanwhocodes/object-schema" "^2.0.2"
      debug "^4.3.1"
      minimatch "^3.0.5"
  
  "@humanwhocodes/module-importer@^1.0.1":
    version "1.0.1"
    resolved "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
    integrity sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==
  
  "@humanwhocodes/object-schema@^2.0.2":
    version "2.0.3"
    resolved "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz"
    integrity sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==
  
  "@isaacs/cliui@^8.0.2":
    version "8.0.2"
    resolved "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz"
    integrity sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==
    dependencies:
      string-width "^5.1.2"
      string-width-cjs "npm:string-width@^4.2.0"
      strip-ansi "^7.0.1"
      strip-ansi-cjs "npm:strip-ansi@^6.0.1"
      wrap-ansi "^8.1.0"
      wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"
  
  "@jridgewell/gen-mapping@^0.3.2":
    version "0.3.5"
    resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz"
    integrity sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==
    dependencies:
      "@jridgewell/set-array" "^1.2.1"
      "@jridgewell/sourcemap-codec" "^1.4.10"
      "@jridgewell/trace-mapping" "^0.3.24"
  
  "@jridgewell/resolve-uri@^3.1.0":
    version "3.1.2"
    resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
    integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==
  
  "@jridgewell/set-array@^1.2.1":
    version "1.2.1"
    resolved "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz"
    integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==
  
  "@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
    version "1.4.15"
    resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz"
    integrity sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==
  
  "@jridgewell/trace-mapping@^0.3.24":
    version "0.3.25"
    resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
    integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
    dependencies:
      "@jridgewell/resolve-uri" "^3.1.0"
      "@jridgewell/sourcemap-codec" "^1.4.14"
  
  "@next/env@14.2.4":
    version "14.2.4"
    resolved "https://registry.npmjs.org/@next/env/-/env-14.2.4.tgz"
    integrity sha512-3EtkY5VDkuV2+lNmKlbkibIJxcO4oIHEhBWne6PaAp+76J9KoSsGvNikp6ivzAT8dhhBMYrm6op2pS1ApG0Hzg==
  
  "@next/eslint-plugin-next@14.2.4":
    version "14.2.4"
    resolved "https://registry.npmjs.org/@next/eslint-plugin-next/-/eslint-plugin-next-14.2.4.tgz"
    integrity sha512-svSFxW9f3xDaZA3idQmlFw7SusOuWTpDTAeBlO3AEPDltrraV+lqs7mAc6A27YdnpQVVIA3sODqUAAHdWhVWsA==
    dependencies:
      glob "10.3.10"
  
  "@next/swc-win32-x64-msvc@14.2.4":
    version "14.2.4"
    resolved "https://registry.npmjs.org/@next/swc-win32-x64-msvc/-/swc-win32-x64-msvc-14.2.4.tgz"
    integrity sha512-tkLrjBzqFTP8DVrAAQmZelEahfR9OxWpFR++vAI9FBhCiIxtwHwBHC23SBHCTURBtwB4kc/x44imVOnkKGNVGg==
  
  "@nodelib/fs.scandir@2.1.5":
    version "2.1.5"
    resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
    integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
    dependencies:
      "@nodelib/fs.stat" "2.0.5"
      run-parallel "^1.1.9"
  
  "@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
    version "2.0.5"
    resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
    integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==
  
  "@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
    version "1.2.8"
    resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
    integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
    dependencies:
      "@nodelib/fs.scandir" "2.1.5"
      fastq "^1.6.0"
  
  "@pkgjs/parseargs@^0.11.0":
    version "0.11.0"
    resolved "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
    integrity sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==
  
  "@pkgr/core@^0.1.0":
    version "0.1.1"
    resolved "https://registry.npmjs.org/@pkgr/core/-/core-0.1.1.tgz"
    integrity sha512-cq8o4cWH0ibXh9VGi5P20Tu9XF/0fFXl9EUinr9QfTM7a7p0oTA4iJRCQWppXR1Pg8dSM0UCItCkPwsk9qWWYA==
  
  "@radix-ui/primitive@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.0.tgz"
    integrity sha512-4Z8dn6Upk0qk4P74xBhZ6Hd/w0mPEzOOLxy4xiPXOXqjF7jZS0VAKk7/x/H6FyY2zCkYJqePf1G5KmkmNJ4RBA==
  
  "@radix-ui/react-arrow@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.0.tgz"
    integrity sha512-FmlW1rCg7hBpEBwFbjHwCW6AmWLQM6g/v0Sn8XbP9NvmSZ2San1FpQeyPtufzOMSIx7Y4dzjlHoifhp+7NkZhw==
    dependencies:
      "@radix-ui/react-primitive" "2.0.0"
  
  "@radix-ui/react-collection@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.0.tgz"
    integrity sha512-GZsZslMJEyo1VKm5L1ZJY8tGDxZNPAoUeQUIbKeJfoi7Q4kmig5AsgLMYYuyYbfjd8fBmFORAIwYAkXMnXZgZw==
    dependencies:
      "@radix-ui/react-compose-refs" "1.1.0"
      "@radix-ui/react-context" "1.1.0"
      "@radix-ui/react-primitive" "2.0.0"
      "@radix-ui/react-slot" "1.1.0"
  
  "@radix-ui/react-compose-refs@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.0.tgz"
    integrity sha512-b4inOtiaOnYf9KWyO3jAeeCG6FeyfY6ldiEPanbUjWd+xIk5wZeHa8yVwmrJ2vderhu/BQvzCrJI0lHd+wIiqw==
  
  "@radix-ui/react-context@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.0.tgz"
    integrity sha512-OKrckBy+sMEgYM/sMmqmErVn0kZqrHPJze+Ql3DzYsDDp0hl0L62nx/2122/Bvps1qz645jlcu2tD9lrRSdf8A==
  
  "@radix-ui/react-direction@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.0.tgz"
    integrity sha512-BUuBvgThEiAXh2DWu93XsT+a3aWrGqolGlqqw5VU1kG7p/ZH2cuDlM1sRLNnY3QcBS69UIz2mcKhMxDsdewhjg==
  
  "@radix-ui/react-dismissable-layer@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.0.tgz"
    integrity sha512-/UovfmmXGptwGcBQawLzvn2jOfM0t4z3/uKffoBlj724+n3FvBbZ7M0aaBOmkp6pqFYpO4yx8tSVJjx3Fl2jig==
    dependencies:
      "@radix-ui/primitive" "1.1.0"
      "@radix-ui/react-compose-refs" "1.1.0"
      "@radix-ui/react-primitive" "2.0.0"
      "@radix-ui/react-use-callback-ref" "1.1.0"
      "@radix-ui/react-use-escape-keydown" "1.1.0"
  
  "@radix-ui/react-focus-guards@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.0.tgz"
    integrity sha512-w6XZNUPVv6xCpZUqb/yN9DL6auvpGX3C/ee6Hdi16v2UUy25HV2Q5bcflsiDyT/g5RwbPQ/GIT1vLkeRb+ITBw==
  
  "@radix-ui/react-focus-scope@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.0.tgz"
    integrity sha512-200UD8zylvEyL8Bx+z76RJnASR2gRMuxlgFCPAe/Q/679a/r0eK3MBVYMb7vZODZcffZBdob1EGnky78xmVvcA==
    dependencies:
      "@radix-ui/react-compose-refs" "1.1.0"
      "@radix-ui/react-primitive" "2.0.0"
      "@radix-ui/react-use-callback-ref" "1.1.0"
  
  "@radix-ui/react-icons@^1.3.0":
    version "1.3.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-icons/-/react-icons-1.3.0.tgz"
    integrity sha512-jQxj/0LKgp+j9BiTXz3O3sgs26RNet2iLWmsPyRz2SIcR4q/4SbazXfnYwbAr+vLYKSfc7qxzyGQA1HLlYiuNw==
  
  "@radix-ui/react-id@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.0.tgz"
    integrity sha512-EJUrI8yYh7WOjNOqpoJaf1jlFIH2LvtgAl+YcFqNCa+4hj64ZXmPkAKOFs/ukjz3byN6bdb/AVUqHkI8/uWWMA==
    dependencies:
      "@radix-ui/react-use-layout-effect" "1.1.0"
  
  "@radix-ui/react-label@^2.1.0":
    version "2.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.0.tgz"
    integrity sha512-peLblDlFw/ngk3UWq0VnYaOLy6agTZZ+MUO/WhVfm14vJGML+xH4FAl2XQGLqdefjNb7ApRg6Yn7U42ZhmYXdw==
    dependencies:
      "@radix-ui/react-primitive" "2.0.0"
  
  "@radix-ui/react-menu@2.1.1":
    version "2.1.1"
    resolved "https://registry.npmjs.org/@radix-ui/react-menu/-/react-menu-2.1.1.tgz"
    integrity sha512-oa3mXRRVjHi6DZu/ghuzdylyjaMXLymx83irM7hTxutQbD+7IhPKdMdRHD26Rm+kHRrWcrUkkRPv5pd47a2xFQ==
    dependencies:
      "@radix-ui/primitive" "1.1.0"
      "@radix-ui/react-collection" "1.1.0"
      "@radix-ui/react-compose-refs" "1.1.0"
      "@radix-ui/react-context" "1.1.0"
      "@radix-ui/react-direction" "1.1.0"
      "@radix-ui/react-dismissable-layer" "1.1.0"
      "@radix-ui/react-focus-guards" "1.1.0"
      "@radix-ui/react-focus-scope" "1.1.0"
      "@radix-ui/react-id" "1.1.0"
      "@radix-ui/react-popper" "1.2.0"
      "@radix-ui/react-portal" "1.1.1"
      "@radix-ui/react-presence" "1.1.0"
      "@radix-ui/react-primitive" "2.0.0"
      "@radix-ui/react-roving-focus" "1.1.0"
      "@radix-ui/react-slot" "1.1.0"
      "@radix-ui/react-use-callback-ref" "1.1.0"
      aria-hidden "^1.1.1"
      react-remove-scroll "2.5.7"
  
  "@radix-ui/react-menubar@^1.1.1":
    version "1.1.1"
    resolved "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.1.tgz"
    integrity sha512-V05Hryq/BE2m+rs8d5eLfrS0jmSWSDHEbG7jEyLA5D5J9jTvWj/o3v3xDN9YsOlH6QIkJgiaNDaP+S4T1rdykw==
    dependencies:
      "@radix-ui/primitive" "1.1.0"
      "@radix-ui/react-collection" "1.1.0"
      "@radix-ui/react-compose-refs" "1.1.0"
      "@radix-ui/react-context" "1.1.0"
      "@radix-ui/react-direction" "1.1.0"
      "@radix-ui/react-id" "1.1.0"
      "@radix-ui/react-menu" "2.1.1"
      "@radix-ui/react-primitive" "2.0.0"
      "@radix-ui/react-roving-focus" "1.1.0"
      "@radix-ui/react-use-controllable-state" "1.1.0"
  
  "@radix-ui/react-popper@1.2.0":
    version "1.2.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.0.tgz"
    integrity sha512-ZnRMshKF43aBxVWPWvbj21+7TQCvhuULWJ4gNIKYpRlQt5xGRhLx66tMp8pya2UkGHTSlhpXwmjqltDYHhw7Vg==
    dependencies:
      "@floating-ui/react-dom" "^2.0.0"
      "@radix-ui/react-arrow" "1.1.0"
      "@radix-ui/react-compose-refs" "1.1.0"
      "@radix-ui/react-context" "1.1.0"
      "@radix-ui/react-primitive" "2.0.0"
      "@radix-ui/react-use-callback-ref" "1.1.0"
      "@radix-ui/react-use-layout-effect" "1.1.0"
      "@radix-ui/react-use-rect" "1.1.0"
      "@radix-ui/react-use-size" "1.1.0"
      "@radix-ui/rect" "1.1.0"
  
  "@radix-ui/react-portal@1.1.1":
    version "1.1.1"
    resolved "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.1.tgz"
    integrity sha512-A3UtLk85UtqhzFqtoC8Q0KvR2GbXF3mtPgACSazajqq6A41mEQgo53iPzY4i6BwDxlIFqWIhiQ2G729n+2aw/g==
    dependencies:
      "@radix-ui/react-primitive" "2.0.0"
      "@radix-ui/react-use-layout-effect" "1.1.0"
  
  "@radix-ui/react-presence@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.0.tgz"
    integrity sha512-Gq6wuRN/asf9H/E/VzdKoUtT8GC9PQc9z40/vEr0VCJ4u5XvvhWIrSsCB6vD2/cH7ugTdSfYq9fLJCcM00acrQ==
    dependencies:
      "@radix-ui/react-compose-refs" "1.1.0"
      "@radix-ui/react-use-layout-effect" "1.1.0"
  
  "@radix-ui/react-primitive@2.0.0":
    version "2.0.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.0.tgz"
    integrity sha512-ZSpFm0/uHa8zTvKBDjLFWLo8dkr4MBsiDLz0g3gMUwqgLHz9rTaRRGYDgvZPtBJgYCBKXkS9fzmoySgr8CO6Cw==
    dependencies:
      "@radix-ui/react-slot" "1.1.0"
  
  "@radix-ui/react-roving-focus@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.0.tgz"
    integrity sha512-EA6AMGeq9AEeQDeSH0aZgG198qkfHSbvWTf1HvoDmOB5bBG/qTxjYMWUKMnYiV6J/iP/J8MEFSuB2zRU2n7ODA==
    dependencies:
      "@radix-ui/primitive" "1.1.0"
      "@radix-ui/react-collection" "1.1.0"
      "@radix-ui/react-compose-refs" "1.1.0"
      "@radix-ui/react-context" "1.1.0"
      "@radix-ui/react-direction" "1.1.0"
      "@radix-ui/react-id" "1.1.0"
      "@radix-ui/react-primitive" "2.0.0"
      "@radix-ui/react-use-callback-ref" "1.1.0"
      "@radix-ui/react-use-controllable-state" "1.1.0"
  
  "@radix-ui/react-slot@^1.1.0", "@radix-ui/react-slot@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.0.tgz"
    integrity sha512-FUCf5XMfmW4dtYl69pdS4DbxKy8nj4M7SafBgPllysxmdachynNflAdp/gCsnYWNDnge6tI9onzMp5ARYc1KNw==
    dependencies:
      "@radix-ui/react-compose-refs" "1.1.0"
  
  "@radix-ui/react-toast@^1.2.1":
    version "1.2.1"
    resolved "https://registry.npmjs.org/@radix-ui/react-toast/-/react-toast-1.2.1.tgz"
    integrity sha512-5trl7piMXcZiCq7MW6r8YYmu0bK5qDpTWz+FdEPdKyft2UixkspheYbjbrLXVN5NGKHFbOP7lm8eD0biiSqZqg==
    dependencies:
      "@radix-ui/primitive" "1.1.0"
      "@radix-ui/react-collection" "1.1.0"
      "@radix-ui/react-compose-refs" "1.1.0"
      "@radix-ui/react-context" "1.1.0"
      "@radix-ui/react-dismissable-layer" "1.1.0"
      "@radix-ui/react-portal" "1.1.1"
      "@radix-ui/react-presence" "1.1.0"
      "@radix-ui/react-primitive" "2.0.0"
      "@radix-ui/react-use-callback-ref" "1.1.0"
      "@radix-ui/react-use-controllable-state" "1.1.0"
      "@radix-ui/react-use-layout-effect" "1.1.0"
      "@radix-ui/react-visually-hidden" "1.1.0"
  
  "@radix-ui/react-use-callback-ref@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0.tgz"
    integrity sha512-CasTfvsy+frcFkbXtSJ2Zu9JHpN8TYKxkgJGWbjiZhFivxaeW7rMeZt7QELGVLaYVfFMsKHjb7Ak0nMEe+2Vfw==
  
  "@radix-ui/react-use-controllable-state@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.1.0.tgz"
    integrity sha512-MtfMVJiSr2NjzS0Aa90NPTnvTSg6C/JLCV7ma0W6+OMV78vd8OyRpID+Ng9LxzsPbLeuBnWBA1Nq30AtBIDChw==
    dependencies:
      "@radix-ui/react-use-callback-ref" "1.1.0"
  
  "@radix-ui/react-use-escape-keydown@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0.tgz"
    integrity sha512-L7vwWlR1kTTQ3oh7g1O0CBF3YCyyTj8NmhLR+phShpyA50HCfBFKVJTpshm9PzLiKmehsrQzTYTpX9HvmC9rhw==
    dependencies:
      "@radix-ui/react-use-callback-ref" "1.1.0"
  
  "@radix-ui/react-use-layout-effect@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.0.tgz"
    integrity sha512-+FPE0rOdziWSrH9athwI1R0HDVbWlEhd+FR+aSDk4uWGmSJ9Z54sdZVDQPZAinJhJXwfT+qnj969mCsT2gfm5w==
  
  "@radix-ui/react-use-rect@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.0.tgz"
    integrity sha512-0Fmkebhr6PiseyZlYAOtLS+nb7jLmpqTrJyv61Pe68MKYW6OWdRE2kI70TaYY27u7H0lajqM3hSMMLFq18Z7nQ==
    dependencies:
      "@radix-ui/rect" "1.1.0"
  
  "@radix-ui/react-use-size@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-use-size/-/react-use-size-1.1.0.tgz"
    integrity sha512-XW3/vWuIXHa+2Uwcc2ABSfcCledmXhhQPlGbfcRXbiUQI5Icjcg19BGCZVKKInYbvUCut/ufbbLLPFC5cbb1hw==
    dependencies:
      "@radix-ui/react-use-layout-effect" "1.1.0"
  
  "@radix-ui/react-visually-hidden@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.1.0.tgz"
    integrity sha512-N8MDZqtgCgG5S3aV60INAB475osJousYpZ4cTJ2cFbMpdHS5Y6loLTH8LPtkj2QN0x93J30HT/M3qJXM0+lyeQ==
    dependencies:
      "@radix-ui/react-primitive" "2.0.0"
  
  "@radix-ui/rect@1.1.0":
    version "1.1.0"
    resolved "https://registry.npmjs.org/@radix-ui/rect/-/rect-1.1.0.tgz"
    integrity sha512-A9+lCBZoaMJlVKcRBz2YByCG+Cp2t6nAnMnNba+XiWxnj6r4JUFqfsgwocMBZU9LPtdxC6wB56ySYpc7LQIoJg==
  
  "@rushstack/eslint-patch@^1.3.3":
    version "1.10.3"
    resolved "https://registry.npmjs.org/@rushstack/eslint-patch/-/eslint-patch-1.10.3.tgz"
    integrity sha512-qC/xYId4NMebE6w/V33Fh9gWxLgURiNYgVNObbJl2LZv0GUUItCcCqC5axQSwRaAgaxl2mELq1rMzlswaQ0Zxg==
  
  "@swc/counter@^0.1.3":
    version "0.1.3"
    resolved "https://registry.npmjs.org/@swc/counter/-/counter-0.1.3.tgz"
    integrity sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==
  
  "@swc/helpers@0.5.5":
    version "0.5.5"
    resolved "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.5.tgz"
    integrity sha512-KGYxvIOXcceOAbEk4bi/dVLEK9z8sZ0uBB3Il5b1rhfClSpcX0yfRO0KmTkqR2cnQDymwLB+25ZyMzICg/cm/A==
    dependencies:
      "@swc/counter" "^0.1.3"
      tslib "^2.4.0"
  
  "@types/json5@^0.0.29":
    version "0.0.29"
    resolved "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz"
    integrity sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==
  
  "@types/node@^20":
    version "20.14.9"
    resolved "https://registry.npmjs.org/@types/node/-/node-20.14.9.tgz"
    integrity sha512-06OCtnTXtWOZBJlRApleWndH4JsRVs1pDCc8dLSQp+7PpUpX3ePdHyeNSFTeSe7FtKyQkrlPvHwJOW3SLd8Oyg==
    dependencies:
      undici-types "~5.26.4"
  
  "@types/prop-types@*":
    version "15.7.12"
    resolved "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.12.tgz"
    integrity sha512-5zvhXYtRNRluoE/jAp4GVsSduVUzNWKkOZrCDBWYtE7biZywwdC2AcEzg+cSMLFRfVgeAFqpfNabiPjxFddV1Q==
  
  "@types/react-dom@^18":
    version "18.3.0"
    resolved "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.3.0.tgz"
    integrity sha512-EhwApuTmMBmXuFOikhQLIBUn6uFg81SwLMOAUgodJF14SOBOCMdU04gDoYi0WOJJHD144TL32z4yDqCW3dnkQg==
    dependencies:
      "@types/react" "*"
  
  "@types/react@*", "@types/react@^18":
    version "18.3.3"
    resolved "https://registry.npmjs.org/@types/react/-/react-18.3.3.tgz"
    integrity sha512-hti/R0pS0q1/xx+TsI73XIqk26eBsISZ2R0wUijXIngRK9R/e7Xw/cXVxQK7R5JjW+SV4zGcn5hXjudkN/pLIw==
    dependencies:
      "@types/prop-types" "*"
      csstype "^3.0.2"
  
  "@typescript-eslint/eslint-plugin@^7.14.1":
    version "7.14.1"
    resolved "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-7.14.1.tgz"
    integrity sha512-aAJd6bIf2vvQRjUG3ZkNXkmBpN+J7Wd0mfQiiVCJMu9Z5GcZZdcc0j8XwN/BM97Fl7e3SkTXODSk4VehUv7CGw==
    dependencies:
      "@eslint-community/regexpp" "^4.10.0"
      "@typescript-eslint/scope-manager" "7.14.1"
      "@typescript-eslint/type-utils" "7.14.1"
      "@typescript-eslint/utils" "7.14.1"
      "@typescript-eslint/visitor-keys" "7.14.1"
      graphemer "^1.4.0"
      ignore "^5.3.1"
      natural-compare "^1.4.0"
      ts-api-utils "^1.3.0"
  
  "@typescript-eslint/parser@^5.4.2 || ^6.0.0 || 7.0.0 - 7.2.0":
    version "7.2.0"
    resolved "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-7.2.0.tgz"
    integrity sha512-5FKsVcHTk6TafQKQbuIVkXq58Fnbkd2wDL4LB7AURN7RUOu1utVP+G8+6u3ZhEroW3DF6hyo3ZEXxgKgp4KeCg==
    dependencies:
      "@typescript-eslint/scope-manager" "7.2.0"
      "@typescript-eslint/types" "7.2.0"
      "@typescript-eslint/typescript-estree" "7.2.0"
      "@typescript-eslint/visitor-keys" "7.2.0"
      debug "^4.3.4"
  
  "@typescript-eslint/scope-manager@7.14.1":
    version "7.14.1"
    resolved "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-7.14.1.tgz"
    integrity sha512-gPrFSsoYcsffYXTOZ+hT7fyJr95rdVe4kGVX1ps/dJ+DfmlnjFN/GcMxXcVkeHDKqsq6uAcVaQaIi3cFffmAbA==
    dependencies:
      "@typescript-eslint/types" "7.14.1"
      "@typescript-eslint/visitor-keys" "7.14.1"
  
  "@typescript-eslint/scope-manager@7.2.0":
    version "7.2.0"
    resolved "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-7.2.0.tgz"
    integrity sha512-Qh976RbQM/fYtjx9hs4XkayYujB/aPwglw2choHmf3zBjB4qOywWSdt9+KLRdHubGcoSwBnXUH2sR3hkyaERRg==
    dependencies:
      "@typescript-eslint/types" "7.2.0"
      "@typescript-eslint/visitor-keys" "7.2.0"
  
  "@typescript-eslint/type-utils@7.14.1":
    version "7.14.1"
    resolved "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-7.14.1.tgz"
    integrity sha512-/MzmgNd3nnbDbOi3LfasXWWe292+iuo+umJ0bCCMCPc1jLO/z2BQmWUUUXvXLbrQey/JgzdF/OV+I5bzEGwJkQ==
    dependencies:
      "@typescript-eslint/typescript-estree" "7.14.1"
      "@typescript-eslint/utils" "7.14.1"
      debug "^4.3.4"
      ts-api-utils "^1.3.0"
  
  "@typescript-eslint/types@7.14.1":
    version "7.14.1"
    resolved "https://registry.npmjs.org/@typescript-eslint/types/-/types-7.14.1.tgz"
    integrity sha512-mL7zNEOQybo5R3AavY+Am7KLv8BorIv7HCYS5rKoNZKQD9tsfGUpO4KdAn3sSUvTiS4PQkr2+K0KJbxj8H9NDg==
  
  "@typescript-eslint/types@7.2.0":
    version "7.2.0"
    resolved "https://registry.npmjs.org/@typescript-eslint/types/-/types-7.2.0.tgz"
    integrity sha512-XFtUHPI/abFhm4cbCDc5Ykc8npOKBSJePY3a3s+lwumt7XWJuzP5cZcfZ610MIPHjQjNsOLlYK8ASPaNG8UiyA==
  
  "@typescript-eslint/typescript-estree@7.14.1":
    version "7.14.1"
    resolved "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-7.14.1.tgz"
    integrity sha512-k5d0VuxViE2ulIO6FbxxSZaxqDVUyMbXcidC8rHvii0I56XZPv8cq+EhMns+d/EVIL41sMXqRbK3D10Oza1bbA==
    dependencies:
      "@typescript-eslint/types" "7.14.1"
      "@typescript-eslint/visitor-keys" "7.14.1"
      debug "^4.3.4"
      globby "^11.1.0"
      is-glob "^4.0.3"
      minimatch "^9.0.4"
      semver "^7.6.0"
      ts-api-utils "^1.3.0"
  
  "@typescript-eslint/typescript-estree@7.2.0":
    version "7.2.0"
    resolved "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-7.2.0.tgz"
    integrity sha512-cyxS5WQQCoBwSakpMrvMXuMDEbhOo9bNHHrNcEWis6XHx6KF518tkF1wBvKIn/tpq5ZpUYK7Bdklu8qY0MsFIA==
    dependencies:
      "@typescript-eslint/types" "7.2.0"
      "@typescript-eslint/visitor-keys" "7.2.0"
      debug "^4.3.4"
      globby "^11.1.0"
      is-glob "^4.0.3"
      minimatch "9.0.3"
      semver "^7.5.4"
      ts-api-utils "^1.0.1"
  
  "@typescript-eslint/utils@7.14.1":
    version "7.14.1"
    resolved "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-7.14.1.tgz"
    integrity sha512-CMmVVELns3nak3cpJhZosDkm63n+DwBlDX8g0k4QUa9BMnF+lH2lr3d130M1Zt1xxmB3LLk3NV7KQCq86ZBBhQ==
    dependencies:
      "@eslint-community/eslint-utils" "^4.4.0"
      "@typescript-eslint/scope-manager" "7.14.1"
      "@typescript-eslint/types" "7.14.1"
      "@typescript-eslint/typescript-estree" "7.14.1"
  
  "@typescript-eslint/visitor-keys@7.14.1":
    version "7.14.1"
    resolved "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-7.14.1.tgz"
    integrity sha512-Crb+F75U1JAEtBeQGxSKwI60hZmmzaqA3z9sYsVm8X7W5cwLEm5bRe0/uXS6+MR/y8CVpKSR/ontIAIEPFcEkA==
    dependencies:
      "@typescript-eslint/types" "7.14.1"
      eslint-visitor-keys "^3.4.3"
  
  "@typescript-eslint/visitor-keys@7.2.0":
    version "7.2.0"
    resolved "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-7.2.0.tgz"
    integrity sha512-c6EIQRHhcpl6+tO8EMR+kjkkV+ugUNXOmeASA1rlzkd8EPIriavpWoiEz1HR/VLhbVIdhqnV6E7JZm00cBDx2A==
    dependencies:
      "@typescript-eslint/types" "7.2.0"
      eslint-visitor-keys "^3.4.1"
  
  "@ungap/structured-clone@^1.2.0":
    version "1.2.0"
    resolved "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.2.0.tgz"
    integrity sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ==
  
  acorn-jsx@^5.3.2:
    version "5.3.2"
    resolved "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
    integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==
  
  acorn@^8.9.0:
    version "8.12.0"
    resolved "https://registry.npmjs.org/acorn/-/acorn-8.12.0.tgz"
    integrity sha512-RTvkC4w+KNXrM39/lWCUaG0IbRkWdCv7W/IOW9oU6SawyxulvkQy5HQPVTKxEjczcUvapcrw3cFx/60VN/NRNw==
  
  ajv@^6.12.4:
    version "6.12.6"
    resolved "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
    integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
    dependencies:
      fast-deep-equal "^3.1.1"
      fast-json-stable-stringify "^2.0.0"
      json-schema-traverse "^0.4.1"
      uri-js "^4.2.2"
  
  ansi-regex@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
    integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==
  
  ansi-regex@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.0.1.tgz"
    integrity sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==
  
  ansi-styles@^4.0.0, ansi-styles@^4.1.0:
    version "4.3.0"
    resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
    integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
    dependencies:
      color-convert "^2.0.1"
  
  ansi-styles@^6.1.0:
    version "6.2.1"
    resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz"
    integrity sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==
  
  any-promise@^1.0.0:
    version "1.3.0"
    resolved "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz"
    integrity sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==
  
  anymatch@~3.1.2:
    version "3.1.3"
    resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
    integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
    dependencies:
      normalize-path "^3.0.0"
      picomatch "^2.0.4"
  
  arg@^5.0.2:
    version "5.0.2"
    resolved "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz"
    integrity sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==
  
  argparse@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
    integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==
  
  aria-hidden@^1.1.1:
    version "1.2.4"
    resolved "https://registry.npmjs.org/aria-hidden/-/aria-hidden-1.2.4.tgz"
    integrity sha512-y+CcFFwelSXpLZk/7fMB2mUbGtX9lKycf1MWJ7CaTIERyitVlyQx6C+sxcROU2BAJ24OiZyK+8wj2i8AlBoS3A==
    dependencies:
      tslib "^2.0.0"
  
  aria-query@~5.1.3:
    version "5.1.3"
    resolved "https://registry.npmjs.org/aria-query/-/aria-query-5.1.3.tgz"
    integrity sha512-R5iJ5lkuHybztUfuOAznmboyjWq8O6sqNqtK7CLOqdydi54VNbORp49mb14KbWgG1QD3JFO9hJdZ+y4KutfdOQ==
    dependencies:
      deep-equal "^2.0.5"
  
  array-buffer-byte-length@^1.0.0, array-buffer-byte-length@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.1.tgz"
    integrity sha512-ahC5W1xgou+KTXix4sAO8Ki12Q+jf4i0+tmk3sC+zgcynshkHxzpXdImBehiUYKKKDwvfFiJl1tZt6ewscS1Mg==
    dependencies:
      call-bind "^1.0.5"
      is-array-buffer "^3.0.4"
  
  array-includes@^3.1.6, array-includes@^3.1.7, array-includes@^3.1.8:
    version "3.1.8"
    resolved "https://registry.npmjs.org/array-includes/-/array-includes-3.1.8.tgz"
    integrity sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.2"
      es-object-atoms "^1.0.0"
      get-intrinsic "^1.2.4"
      is-string "^1.0.7"
  
  array-union@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz"
    integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==
  
  array.prototype.findlast@^1.2.5:
    version "1.2.5"
    resolved "https://registry.npmjs.org/array.prototype.findlast/-/array.prototype.findlast-1.2.5.tgz"
    integrity sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.2"
      es-errors "^1.3.0"
      es-object-atoms "^1.0.0"
      es-shim-unscopables "^1.0.2"
  
  array.prototype.findlastindex@^1.2.3:
    version "1.2.5"
    resolved "https://registry.npmjs.org/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.5.tgz"
    integrity sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.2"
      es-errors "^1.3.0"
      es-object-atoms "^1.0.0"
      es-shim-unscopables "^1.0.2"
  
  array.prototype.flat@^1.3.1, array.prototype.flat@^1.3.2:
    version "1.3.2"
    resolved "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.2.tgz"
    integrity sha512-djYB+Zx2vLewY8RWlNCUdHjDXs2XOgm602S9E7P/UpHgfeHL00cRiIF+IN/G/aUJ7kGPb6yO/ErDI5V2s8iycA==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.2.0"
      es-abstract "^1.22.1"
      es-shim-unscopables "^1.0.0"
  
  array.prototype.flatmap@^1.3.2:
    version "1.3.2"
    resolved "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.2.tgz"
    integrity sha512-Ewyx0c9PmpcsByhSW4r+9zDU7sGjFc86qf/kKtuSCRdhfbk0SNLLkaT5qvcHnRGgc5NP/ly/y+qkXkqONX54CQ==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.2.0"
      es-abstract "^1.22.1"
      es-shim-unscopables "^1.0.0"
  
  array.prototype.toreversed@^1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/array.prototype.toreversed/-/array.prototype.toreversed-1.1.2.tgz"
    integrity sha512-wwDCoT4Ck4Cz7sLtgUmzR5UV3YF5mFHUlbChCzZBQZ+0m2cl/DH3tKgvphv1nKgFsJ48oCSg6p91q2Vm0I/ZMA==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.2.0"
      es-abstract "^1.22.1"
      es-shim-unscopables "^1.0.0"
  
  array.prototype.tosorted@^1.1.4:
    version "1.1.4"
    resolved "https://registry.npmjs.org/array.prototype.tosorted/-/array.prototype.tosorted-1.1.4.tgz"
    integrity sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.3"
      es-errors "^1.3.0"
      es-shim-unscopables "^1.0.2"
  
  arraybuffer.prototype.slice@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.3.tgz"
    integrity sha512-bMxMKAjg13EBSVscxTaYA4mRc5t1UAXa2kXiGTNfZ079HIWXEkKmkgFrh/nJqamaLSrXO5H4WFFkPEaLJWbs3A==
    dependencies:
      array-buffer-byte-length "^1.0.1"
      call-bind "^1.0.5"
      define-properties "^1.2.1"
      es-abstract "^1.22.3"
      es-errors "^1.2.1"
      get-intrinsic "^1.2.3"
      is-array-buffer "^3.0.4"
      is-shared-array-buffer "^1.0.2"
  
  ast-types-flow@^0.0.8:
    version "0.0.8"
    resolved "https://registry.npmjs.org/ast-types-flow/-/ast-types-flow-0.0.8.tgz"
    integrity sha512-OH/2E5Fg20h2aPrbe+QL8JZQFko0YZaF+j4mnQ7BGhfavO7OpSLa8a0y9sBwomHdSbkhTS8TQNayBfnW5DwbvQ==
  
  available-typed-arrays@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz"
    integrity sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==
    dependencies:
      possible-typed-array-names "^1.0.0"
  
  axe-core@^4.9.1:
    version "4.9.1"
    resolved "https://registry.npmjs.org/axe-core/-/axe-core-4.9.1.tgz"
    integrity sha512-QbUdXJVTpvUTHU7871ppZkdOLBeGUKBQWHkHrvN2V9IQWGMt61zf3B45BtzjxEJzYuj0JBjBZP/hmYS/R9pmAw==
  
  axobject-query@~3.1.1:
    version "3.1.1"
    resolved "https://registry.npmjs.org/axobject-query/-/axobject-query-3.1.1.tgz"
    integrity sha512-goKlv8DZrK9hUh975fnHzhNIO4jUnFCfv/dszV5VwUGDFjI6vQ2VwoyjYjYNEbBE8AH87TduWP5uyDR1D+Iteg==
    dependencies:
      deep-equal "^2.0.5"
  
  balanced-match@^1.0.0:
    version "1.0.2"
    resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
    integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==
  
  binary-extensions@^2.0.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz"
    integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==
  
  brace-expansion@^1.1.7:
    version "1.1.11"
    resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
    integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
    dependencies:
      balanced-match "^1.0.0"
      concat-map "0.0.1"
  
  brace-expansion@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
    integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
    dependencies:
      balanced-match "^1.0.0"
  
  braces@^3.0.3, braces@~3.0.2:
    version "3.0.3"
    resolved "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
    integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
    dependencies:
      fill-range "^7.1.1"
  
  busboy@1.6.0:
    version "1.6.0"
    resolved "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz"
    integrity sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==
    dependencies:
      streamsearch "^1.1.0"
  
  call-bind@^1.0.2, call-bind@^1.0.5, call-bind@^1.0.6, call-bind@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.7.tgz"
    integrity sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==
    dependencies:
      es-define-property "^1.0.0"
      es-errors "^1.3.0"
      function-bind "^1.1.2"
      get-intrinsic "^1.2.4"
      set-function-length "^1.2.1"
  
  callsites@^3.0.0:
    version "3.1.0"
    resolved "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
    integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==
  
  camelcase-css@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz"
    integrity sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==
  
  caniuse-lite@^1.0.30001579:
    version "1.0.30001639"
    resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001639.tgz"
    integrity sha512-eFHflNTBIlFwP2AIKaYuBQN/apnUoKNhBdza8ZnW/h2di4LCZ4xFqYlxUxo+LQ76KFI1PGcC1QDxMbxTZpSCAg==
  
  chalk@^4.0.0:
    version "4.1.2"
    resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
    integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
    dependencies:
      ansi-styles "^4.1.0"
      supports-color "^7.1.0"
  
  chokidar@^3.5.3:
    version "3.6.0"
    resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
    integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
    dependencies:
      anymatch "~3.1.2"
      braces "~3.0.2"
      glob-parent "~5.1.2"
      is-binary-path "~2.1.0"
      is-glob "~4.0.1"
      normalize-path "~3.0.0"
      readdirp "~3.6.0"
    optionalDependencies:
      fsevents "~2.3.2"
  
  class-variance-authority@^0.7.0:
    version "0.7.0"
    resolved "https://registry.npmjs.org/class-variance-authority/-/class-variance-authority-0.7.0.tgz"
    integrity sha512-jFI8IQw4hczaL4ALINxqLEXQbWcNjoSkloa4IaufXCJr6QawJyw7tuRysRsrE8w2p/4gGaxKIt/hX3qz/IbD1A==
    dependencies:
      clsx "2.0.0"
  
  classnames@^2.2.5:
    version "2.5.1"
    resolved "https://registry.npmjs.org/classnames/-/classnames-2.5.1.tgz"
    integrity sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==
  
  client-only@0.0.1:
    version "0.0.1"
    resolved "https://registry.npmjs.org/client-only/-/client-only-0.0.1.tgz"
    integrity sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==
  
  clsx@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz"
    integrity sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==
  
  clsx@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/clsx/-/clsx-2.0.0.tgz"
    integrity sha512-rQ1+kcj+ttHG0MKVGBUXwayCCF1oh39BF5COIpRzuCEv8Mwjv0XucrI2ExNTOn9IlLifGClWQcU9BrZORvtw6Q==
  
  color-convert@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
    integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
    dependencies:
      color-name "~1.1.4"
  
  color-name@~1.1.4:
    version "1.1.4"
    resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
    integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==
  
  commander@^4.0.0:
    version "4.1.1"
    resolved "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz"
    integrity sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==
  
  concat-map@0.0.1:
    version "0.0.1"
    resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
    integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==
  
  cross-spawn@^7.0.0, cross-spawn@^7.0.2:
    version "7.0.3"
    resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
    integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
    dependencies:
      path-key "^3.1.0"
      shebang-command "^2.0.0"
      which "^2.0.1"
  
  cssesc@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
    integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==
  
  csstype@^3.0.2:
    version "3.1.3"
    resolved "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz"
    integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==
  
  damerau-levenshtein@^1.0.8:
    version "1.0.8"
    resolved "https://registry.npmjs.org/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz"
    integrity sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==
  
  data-view-buffer@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/data-view-buffer/-/data-view-buffer-1.0.1.tgz"
    integrity sha512-0lht7OugA5x3iJLOWFhWK/5ehONdprk0ISXqVFn/NFrDu+cuc8iADFrGQz5BnRK7LLU3JmkbXSxaqX+/mXYtUA==
    dependencies:
      call-bind "^1.0.6"
      es-errors "^1.3.0"
      is-data-view "^1.0.1"
  
  data-view-byte-length@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/data-view-byte-length/-/data-view-byte-length-1.0.1.tgz"
    integrity sha512-4J7wRJD3ABAzr8wP+OcIcqq2dlUKp4DVflx++hs5h5ZKydWMI6/D/fAot+yh6g2tHh8fLFTvNOaVN357NvSrOQ==
    dependencies:
      call-bind "^1.0.7"
      es-errors "^1.3.0"
      is-data-view "^1.0.1"
  
  data-view-byte-offset@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/data-view-byte-offset/-/data-view-byte-offset-1.0.0.tgz"
    integrity sha512-t/Ygsytq+R995EJ5PZlD4Cu56sWa8InXySaViRzw9apusqsOO2bQP+SbYzAhR0pFKoB+43lYy8rWban9JSuXnA==
    dependencies:
      call-bind "^1.0.6"
      es-errors "^1.3.0"
      is-data-view "^1.0.1"
  
  debug@^3.2.7:
    version "3.2.7"
    resolved "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
    integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
    dependencies:
      ms "^2.1.1"
  
  debug@^4.3.1, debug@^4.3.2, debug@^4.3.4:
    version "4.3.5"
    resolved "https://registry.npmjs.org/debug/-/debug-4.3.5.tgz"
    integrity sha512-pt0bNEmneDIvdL1Xsd9oDQ/wrQRkXDT4AUWlNZNPKvW5x/jyO9VFXkJUP07vQ2upmw5PlaITaPKc31jK13V+jg==
    dependencies:
      ms "2.1.2"
  
  deep-equal@^2.0.5:
    version "2.2.3"
    resolved "https://registry.npmjs.org/deep-equal/-/deep-equal-2.2.3.tgz"
    integrity sha512-ZIwpnevOurS8bpT4192sqAowWM76JDKSHYzMLty3BZGSswgq6pBaH3DhCSW5xVAZICZyKdOBPjwww5wfgT/6PA==
    dependencies:
      array-buffer-byte-length "^1.0.0"
      call-bind "^1.0.5"
      es-get-iterator "^1.1.3"
      get-intrinsic "^1.2.2"
      is-arguments "^1.1.1"
      is-array-buffer "^3.0.2"
      is-date-object "^1.0.5"
      is-regex "^1.1.4"
      is-shared-array-buffer "^1.0.2"
      isarray "^2.0.5"
      object-is "^1.1.5"
      object-keys "^1.1.1"
      object.assign "^4.1.4"
      regexp.prototype.flags "^1.5.1"
      side-channel "^1.0.4"
      which-boxed-primitive "^1.0.2"
      which-collection "^1.0.1"
      which-typed-array "^1.1.13"
  
  deep-is@^0.1.3:
    version "0.1.4"
    resolved "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
    integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==
  
  deepmerge@^4.0.0:
    version "4.3.1"
    resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz"
    integrity sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==
  
  define-data-property@^1.0.1, define-data-property@^1.1.4:
    version "1.1.4"
    resolved "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz"
    integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
    dependencies:
      es-define-property "^1.0.0"
      es-errors "^1.3.0"
      gopd "^1.0.1"
  
  define-properties@^1.1.3, define-properties@^1.2.0, define-properties@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz"
    integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
    dependencies:
      define-data-property "^1.0.1"
      has-property-descriptors "^1.0.0"
      object-keys "^1.1.1"
  
  detect-node-es@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz"
    integrity sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==
  
  didyoumean@^1.2.2:
    version "1.2.2"
    resolved "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz"
    integrity sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==
  
  dir-glob@^3.0.1:
    version "3.0.1"
    resolved "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz"
    integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
    dependencies:
      path-type "^4.0.0"
  
  dlv@^1.1.3:
    version "1.1.3"
    resolved "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz"
    integrity sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==
  
  doctrine@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
    integrity sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==
    dependencies:
      esutils "^2.0.2"
  
  doctrine@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
    integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
    dependencies:
      esutils "^2.0.2"
  
  eastasianwidth@^0.2.0:
    version "0.2.0"
    resolved "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
    integrity sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==
  
  embla-carousel-react@^8.1.6:
    version "8.1.6"
    resolved "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.1.6.tgz"
    integrity sha512-DHxwFzF63yVrU95Eo58E9Xr5b6Y9ul6TTsqb/rtwMi+jXudAmIqN1i9iBxQ73i8jKuUVxll/ziNYMmnWvrdQJQ==
    dependencies:
      embla-carousel "8.1.6"
      embla-carousel-reactive-utils "8.1.6"
  
  embla-carousel-reactive-utils@8.1.6:
    version "8.1.6"
    resolved "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.1.6.tgz"
    integrity sha512-Wg+J2YoqLqkaqsXi7fTJaLmXm6BpgDRJ0EfTdvQ4KE/ip5OsUuKGpJsEQDTt4waGXSDyZhIBlfoQtgGJeyYQ1Q==
  
  embla-carousel@8.1.6:
    version "8.1.6"
    resolved "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.1.6.tgz"
    integrity sha512-9n7FVsbPAs1KD+JmO84DnEDOZMXPBQbLujjMQqvsBRN2CDWwgZ9hRSNapztdPnyJfzOIxowGmj0BUQ8ACYAPkA==
  
  emoji-regex@^8.0.0:
    version "8.0.0"
    resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
    integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==
  
  emoji-regex@^9.2.2:
    version "9.2.2"
    resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz"
    integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==
  
  enhanced-resolve@^5.12.0:
    version "5.17.0"
    resolved "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.17.0.tgz"
    integrity sha512-dwDPwZL0dmye8Txp2gzFmA6sxALaSvdRDjPH0viLcKrtlOL3tw62nWWweVD1SdILDTJrbrL6tdWVN58Wo6U3eA==
    dependencies:
      graceful-fs "^4.2.4"
      tapable "^2.2.0"
  
  es-abstract@^1.17.5, es-abstract@^1.22.1, es-abstract@^1.22.3, es-abstract@^1.23.0, es-abstract@^1.23.1, es-abstract@^1.23.2, es-abstract@^1.23.3:
    version "1.23.3"
    resolved "https://registry.npmjs.org/es-abstract/-/es-abstract-1.23.3.tgz"
    integrity sha512-e+HfNH61Bj1X9/jLc5v1owaLYuHdeHHSQlkhCBiTK8rBvKaULl/beGMxwrMXjpYrv4pz22BlY570vVePA2ho4A==
    dependencies:
      array-buffer-byte-length "^1.0.1"
      arraybuffer.prototype.slice "^1.0.3"
      available-typed-arrays "^1.0.7"
      call-bind "^1.0.7"
      data-view-buffer "^1.0.1"
      data-view-byte-length "^1.0.1"
      data-view-byte-offset "^1.0.0"
      es-define-property "^1.0.0"
      es-errors "^1.3.0"
      es-object-atoms "^1.0.0"
      es-set-tostringtag "^2.0.3"
      es-to-primitive "^1.2.1"
      function.prototype.name "^1.1.6"
      get-intrinsic "^1.2.4"
      get-symbol-description "^1.0.2"
      globalthis "^1.0.3"
      gopd "^1.0.1"
      has-property-descriptors "^1.0.2"
      has-proto "^1.0.3"
      has-symbols "^1.0.3"
      hasown "^2.0.2"
      internal-slot "^1.0.7"
      is-array-buffer "^3.0.4"
      is-callable "^1.2.7"
      is-data-view "^1.0.1"
      is-negative-zero "^2.0.3"
      is-regex "^1.1.4"
      is-shared-array-buffer "^1.0.3"
      is-string "^1.0.7"
      is-typed-array "^1.1.13"
      is-weakref "^1.0.2"
      object-inspect "^1.13.1"
      object-keys "^1.1.1"
      object.assign "^4.1.5"
      regexp.prototype.flags "^1.5.2"
      safe-array-concat "^1.1.2"
      safe-regex-test "^1.0.3"
      string.prototype.trim "^1.2.9"
      string.prototype.trimend "^1.0.8"
      string.prototype.trimstart "^1.0.8"
      typed-array-buffer "^1.0.2"
      typed-array-byte-length "^1.0.1"
      typed-array-byte-offset "^1.0.2"
      typed-array-length "^1.0.6"
      unbox-primitive "^1.0.2"
      which-typed-array "^1.1.15"
  
  es-define-property@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.0.tgz"
    integrity sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==
    dependencies:
      get-intrinsic "^1.2.4"
  
  es-errors@^1.2.1, es-errors@^1.3.0:
    version "1.3.0"
    resolved "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
    integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==
  
  es-get-iterator@^1.1.3:
    version "1.1.3"
    resolved "https://registry.npmjs.org/es-get-iterator/-/es-get-iterator-1.1.3.tgz"
    integrity sha512-sPZmqHBe6JIiTfN5q2pEi//TwxmAFHwj/XEuYjTuse78i8KxaqMTTzxPoFKuzRpDpTJ+0NAbpfenkmH2rePtuw==
    dependencies:
      call-bind "^1.0.2"
      get-intrinsic "^1.1.3"
      has-symbols "^1.0.3"
      is-arguments "^1.1.1"
      is-map "^2.0.2"
      is-set "^2.0.2"
      is-string "^1.0.7"
      isarray "^2.0.5"
      stop-iteration-iterator "^1.0.0"
  
  es-iterator-helpers@^1.0.19:
    version "1.0.19"
    resolved "https://registry.npmjs.org/es-iterator-helpers/-/es-iterator-helpers-1.0.19.tgz"
    integrity sha512-zoMwbCcH5hwUkKJkT8kDIBZSz9I6mVG//+lDCinLCGov4+r7NIy0ld8o03M0cJxl2spVf6ESYVS6/gpIfq1FFw==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.3"
      es-errors "^1.3.0"
      es-set-tostringtag "^2.0.3"
      function-bind "^1.1.2"
      get-intrinsic "^1.2.4"
      globalthis "^1.0.3"
      has-property-descriptors "^1.0.2"
      has-proto "^1.0.3"
      has-symbols "^1.0.3"
      internal-slot "^1.0.7"
      iterator.prototype "^1.1.2"
      safe-array-concat "^1.1.2"
  
  es-object-atoms@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.0.0.tgz"
    integrity sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==
    dependencies:
      es-errors "^1.3.0"
  
  es-set-tostringtag@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.3.tgz"
    integrity sha512-3T8uNMC3OQTHkFUsFq8r/BwAXLHvU/9O9mE0fBc/MY5iq/8H7ncvO947LmYA6ldWw9Uh8Yhf25zu6n7nML5QWQ==
    dependencies:
      get-intrinsic "^1.2.4"
      has-tostringtag "^1.0.2"
      hasown "^2.0.1"
  
  es-shim-unscopables@^1.0.0, es-shim-unscopables@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.0.2.tgz"
    integrity sha512-J3yBRXCzDu4ULnQwxyToo/OjdMx6akgVC7K6few0a7F/0wLtmKKN7I73AH5T2836UuXRqN7Qg+IIUw/+YJksRw==
    dependencies:
      hasown "^2.0.0"
  
  es-to-primitive@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
    integrity sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==
    dependencies:
      is-callable "^1.1.4"
      is-date-object "^1.0.1"
      is-symbol "^1.0.2"
  
  escape-string-regexp@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
    integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==
  
  eslint-config-next@14.2.4:
    version "14.2.4"
    resolved "https://registry.npmjs.org/eslint-config-next/-/eslint-config-next-14.2.4.tgz"
    integrity sha512-Qr0wMgG9m6m4uYy2jrYJmyuNlYZzPRQq5Kvb9IDlYwn+7yq6W6sfMNFgb+9guM1KYwuIo6TIaiFhZJ6SnQ/Efw==
    dependencies:
      "@next/eslint-plugin-next" "14.2.4"
      "@rushstack/eslint-patch" "^1.3.3"
      "@typescript-eslint/parser" "^5.4.2 || ^6.0.0 || 7.0.0 - 7.2.0"
      eslint-import-resolver-node "^0.3.6"
      eslint-import-resolver-typescript "^3.5.2"
      eslint-plugin-import "^2.28.1"
      eslint-plugin-jsx-a11y "^6.7.1"
      eslint-plugin-react "^7.33.2"
      eslint-plugin-react-hooks "^4.5.0 || 5.0.0-canary-7118f5dd7-20230705"
  
  eslint-config-prettier@^9.1.0:
    version "9.1.0"
    resolved "https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-9.1.0.tgz"
    integrity sha512-NSWl5BFQWEPi1j4TjVNItzYV7dZXZ+wP6I6ZhrBGpChQhZRUaElihE9uRRkcbRnNb76UMKDF3r+WTmNcGPKsqw==
  
  eslint-import-resolver-node@^0.3.6, eslint-import-resolver-node@^0.3.9:
    version "0.3.9"
    resolved "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz"
    integrity sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==
    dependencies:
      debug "^3.2.7"
      is-core-module "^2.13.0"
      resolve "^1.22.4"
  
  eslint-import-resolver-typescript@^3.5.2:
    version "3.6.1"
    resolved "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.6.1.tgz"
    integrity sha512-xgdptdoi5W3niYeuQxKmzVDTATvLYqhpwmykwsh7f6HIOStGWEIL9iqZgQDF9u9OEzrRwR8no5q2VT+bjAujTg==
    dependencies:
      debug "^4.3.4"
      enhanced-resolve "^5.12.0"
      eslint-module-utils "^2.7.4"
      fast-glob "^3.3.1"
      get-tsconfig "^4.5.0"
      is-core-module "^2.11.0"
      is-glob "^4.0.3"
  
  eslint-module-utils@^2.7.4, eslint-module-utils@^2.8.0:
    version "2.8.1"
    resolved "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.8.1.tgz"
    integrity sha512-rXDXR3h7cs7dy9RNpUlQf80nX31XWJEyGq1tRMo+6GsO5VmTe4UTwtmonAD4ZkAsrfMVDA2wlGJ3790Ys+D49Q==
    dependencies:
      debug "^3.2.7"
  
  eslint-plugin-import@^2.28.1:
    version "2.29.1"
    resolved "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.29.1.tgz"
    integrity sha512-BbPC0cuExzhiMo4Ff1BTVwHpjjv28C5R+btTOGaCRC7UEz801up0JadwkeSk5Ued6TG34uaczuVuH6qyy5YUxw==
    dependencies:
      array-includes "^3.1.7"
      array.prototype.findlastindex "^1.2.3"
      array.prototype.flat "^1.3.2"
      array.prototype.flatmap "^1.3.2"
      debug "^3.2.7"
      doctrine "^2.1.0"
      eslint-import-resolver-node "^0.3.9"
      eslint-module-utils "^2.8.0"
      hasown "^2.0.0"
      is-core-module "^2.13.1"
      is-glob "^4.0.3"
      minimatch "^3.1.2"
      object.fromentries "^2.0.7"
      object.groupby "^1.0.1"
      object.values "^1.1.7"
      semver "^6.3.1"
      tsconfig-paths "^3.15.0"
  
  eslint-plugin-jsx-a11y@^6.7.1:
    version "6.9.0"
    resolved "https://registry.npmjs.org/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.9.0.tgz"
    integrity sha512-nOFOCaJG2pYqORjK19lqPqxMO/JpvdCZdPtNdxY3kvom3jTvkAbOvQvD8wuD0G8BYR0IGAGYDlzqWJOh/ybn2g==
    dependencies:
      aria-query "~5.1.3"
      array-includes "^3.1.8"
      array.prototype.flatmap "^1.3.2"
      ast-types-flow "^0.0.8"
      axe-core "^4.9.1"
      axobject-query "~3.1.1"
      damerau-levenshtein "^1.0.8"
      emoji-regex "^9.2.2"
      es-iterator-helpers "^1.0.19"
      hasown "^2.0.2"
      jsx-ast-utils "^3.3.5"
      language-tags "^1.0.9"
      minimatch "^3.1.2"
      object.fromentries "^2.0.8"
      safe-regex-test "^1.0.3"
      string.prototype.includes "^2.0.0"
  
  eslint-plugin-prettier@^5.1.3:
    version "5.1.3"
    resolved "https://registry.npmjs.org/eslint-plugin-prettier/-/eslint-plugin-prettier-5.1.3.tgz"
    integrity sha512-C9GCVAs4Eq7ZC/XFQHITLiHJxQngdtraXaM+LoUFoFp/lHNl2Zn8f3WQbe9HvTBBQ9YnKFB0/2Ajdqwo5D1EAw==
    dependencies:
      prettier-linter-helpers "^1.0.0"
      synckit "^0.8.6"
  
  "eslint-plugin-react-hooks@^4.5.0 || 5.0.0-canary-7118f5dd7-20230705":
    version "4.6.2"
    resolved "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.2.tgz"
    integrity sha512-QzliNJq4GinDBcD8gPB5v0wh6g8q3SUi6EFF0x8N/BL9PoVs0atuGc47ozMRyOWAKdwaZ5OnbOEa3WR+dSGKuQ==
  
  eslint-plugin-react@^7.33.2:
    version "7.34.3"
    resolved "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.34.3.tgz"
    integrity sha512-aoW4MV891jkUulwDApQbPYTVZmeuSyFrudpbTAQuj5Fv8VL+o6df2xIGpw8B0hPjAaih1/Fb0om9grCdyFYemA==
    dependencies:
      array-includes "^3.1.8"
      array.prototype.findlast "^1.2.5"
      array.prototype.flatmap "^1.3.2"
      array.prototype.toreversed "^1.1.2"
      array.prototype.tosorted "^1.1.4"
      doctrine "^2.1.0"
      es-iterator-helpers "^1.0.19"
      estraverse "^5.3.0"
      jsx-ast-utils "^2.4.1 || ^3.0.0"
      minimatch "^3.1.2"
      object.entries "^1.1.8"
      object.fromentries "^2.0.8"
      object.hasown "^1.1.4"
      object.values "^1.2.0"
      prop-types "^15.8.1"
      resolve "^2.0.0-next.5"
      semver "^6.3.1"
      string.prototype.matchall "^4.0.11"
  
  eslint-scope@^7.2.2:
    version "7.2.2"
    resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz"
    integrity sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==
    dependencies:
      esrecurse "^4.3.0"
      estraverse "^5.2.0"
  
  eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
    version "3.4.3"
    resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
    integrity sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==
  
  eslint@^8:
    version "8.57.0"
    resolved "https://registry.npmjs.org/eslint/-/eslint-8.57.0.tgz"
    integrity sha512-dZ6+mexnaTIbSBZWgou51U6OmzIhYM2VcNdtiTtI7qPNZm35Akpr0f6vtw3w1Kmn5PYo+tZVfh13WrhpS6oLqQ==
    dependencies:
      "@eslint-community/eslint-utils" "^4.2.0"
      "@eslint-community/regexpp" "^4.6.1"
      "@eslint/eslintrc" "^2.1.4"
      "@eslint/js" "8.57.0"
      "@humanwhocodes/config-array" "^0.11.14"
      "@humanwhocodes/module-importer" "^1.0.1"
      "@nodelib/fs.walk" "^1.2.8"
      "@ungap/structured-clone" "^1.2.0"
      ajv "^6.12.4"
      chalk "^4.0.0"
      cross-spawn "^7.0.2"
      debug "^4.3.2"
      doctrine "^3.0.0"
      escape-string-regexp "^4.0.0"
      eslint-scope "^7.2.2"
      eslint-visitor-keys "^3.4.3"
      espree "^9.6.1"
      esquery "^1.4.2"
      esutils "^2.0.2"
      fast-deep-equal "^3.1.3"
      file-entry-cache "^6.0.1"
      find-up "^5.0.0"
      glob-parent "^6.0.2"
      globals "^13.19.0"
      graphemer "^1.4.0"
      ignore "^5.2.0"
      imurmurhash "^0.1.4"
      is-glob "^4.0.0"
      is-path-inside "^3.0.3"
      js-yaml "^4.1.0"
      json-stable-stringify-without-jsonify "^1.0.1"
      levn "^0.4.1"
      lodash.merge "^4.6.2"
      minimatch "^3.1.2"
      natural-compare "^1.4.0"
      optionator "^0.9.3"
      strip-ansi "^6.0.1"
      text-table "^0.2.0"
  
  espree@^9.6.0, espree@^9.6.1:
    version "9.6.1"
    resolved "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz"
    integrity sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==
    dependencies:
      acorn "^8.9.0"
      acorn-jsx "^5.3.2"
      eslint-visitor-keys "^3.4.1"
  
  esquery@^1.4.2:
    version "1.5.0"
    resolved "https://registry.npmjs.org/esquery/-/esquery-1.5.0.tgz"
    integrity sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==
    dependencies:
      estraverse "^5.1.0"
  
  esrecurse@^4.3.0:
    version "4.3.0"
    resolved "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
    integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
    dependencies:
      estraverse "^5.2.0"
  
  estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
    version "5.3.0"
    resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
    integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==
  
  esutils@^2.0.2:
    version "2.0.3"
    resolved "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
    integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==
  
  fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
    version "3.1.3"
    resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
    integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==
  
  fast-diff@^1.1.2:
    version "1.3.0"
    resolved "https://registry.npmjs.org/fast-diff/-/fast-diff-1.3.0.tgz"
    integrity sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==
  
  fast-glob@^3.2.9, fast-glob@^3.3.0, fast-glob@^3.3.1:
    version "3.3.2"
    resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.2.tgz"
    integrity sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==
    dependencies:
      "@nodelib/fs.stat" "^2.0.2"
      "@nodelib/fs.walk" "^1.2.3"
      glob-parent "^5.1.2"
      merge2 "^1.3.0"
      micromatch "^4.0.4"
  
  fast-json-stable-stringify@^2.0.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
    integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==
  
  fast-levenshtein@^2.0.6:
    version "2.0.6"
    resolved "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
    integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==
  
  fastq@^1.6.0:
    version "1.17.1"
    resolved "https://registry.npmjs.org/fastq/-/fastq-1.17.1.tgz"
    integrity sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==
    dependencies:
      reusify "^1.0.4"
  
  file-entry-cache@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
    integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
    dependencies:
      flat-cache "^3.0.4"
  
  fill-range@^7.1.1:
    version "7.1.1"
    resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
    integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
    dependencies:
      to-regex-range "^5.0.1"
  
  find-up@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
    integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
    dependencies:
      locate-path "^6.0.0"
      path-exists "^4.0.0"
  
  flat-cache@^3.0.4:
    version "3.2.0"
    resolved "https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz"
    integrity sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==
    dependencies:
      flatted "^3.2.9"
      keyv "^4.5.3"
      rimraf "^3.0.2"
  
  flatted@^3.2.9:
    version "3.3.1"
    resolved "https://registry.npmjs.org/flatted/-/flatted-3.3.1.tgz"
    integrity sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==
  
  for-each@^0.3.3:
    version "0.3.3"
    resolved "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz"
    integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
    dependencies:
      is-callable "^1.1.3"
  
  foreground-child@^3.1.0:
    version "3.2.1"
    resolved "https://registry.npmjs.org/foreground-child/-/foreground-child-3.2.1.tgz"
    integrity sha512-PXUUyLqrR2XCWICfv6ukppP96sdFwWbNEnfEMt7jNsISjMsvaLNinAHNDYyvkyU+SZG2BTSbT5NjG+vZslfGTA==
    dependencies:
      cross-spawn "^7.0.0"
      signal-exit "^4.0.1"
  
  fs.realpath@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
    integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==
  
  function-bind@^1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
    integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==
  
  function.prototype.name@^1.1.5, function.prototype.name@^1.1.6:
    version "1.1.6"
    resolved "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.6.tgz"
    integrity sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.2.0"
      es-abstract "^1.22.1"
      functions-have-names "^1.2.3"
  
  functions-have-names@^1.2.3:
    version "1.2.3"
    resolved "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz"
    integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==
  
  get-intrinsic@^1.1.3, get-intrinsic@^1.2.1, get-intrinsic@^1.2.2, get-intrinsic@^1.2.3, get-intrinsic@^1.2.4:
    version "1.2.4"
    resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.4.tgz"
    integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
    dependencies:
      es-errors "^1.3.0"
      function-bind "^1.1.2"
      has-proto "^1.0.1"
      has-symbols "^1.0.3"
      hasown "^2.0.0"
  
  get-nonce@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/get-nonce/-/get-nonce-1.0.1.tgz"
    integrity sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==
  
  get-symbol-description@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.0.2.tgz"
    integrity sha512-g0QYk1dZBxGwk+Ngc+ltRH2IBp2f7zBkBMBJZCDerh6EhlhSR6+9irMCuT/09zD6qkarHUSn529sK/yL4S27mg==
    dependencies:
      call-bind "^1.0.5"
      es-errors "^1.3.0"
      get-intrinsic "^1.2.4"
  
  get-tsconfig@^4.5.0:
    version "4.7.5"
    resolved "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.7.5.tgz"
    integrity sha512-ZCuZCnlqNzjb4QprAzXKdpp/gh6KTxSJuw3IBsPnV/7fV4NxC9ckB+vPTt8w7fJA0TaSD7c55BR47JD6MEDyDw==
    dependencies:
      resolve-pkg-maps "^1.0.0"
  
  glob-parent@^5.1.2, glob-parent@~5.1.2:
    version "5.1.2"
    resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
    integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
    dependencies:
      is-glob "^4.0.1"
  
  glob-parent@^6.0.2:
    version "6.0.2"
    resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
    integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
    dependencies:
      is-glob "^4.0.3"
  
  glob@^10.3.10:
    version "10.4.2"
    resolved "https://registry.npmjs.org/glob/-/glob-10.4.2.tgz"
    integrity sha512-GwMlUF6PkPo3Gk21UxkCohOv0PLcIXVtKyLlpEI28R/cO/4eNOdmLk3CMW1wROV/WR/EsZOWAfBbBOqYvs88/w==
    dependencies:
      foreground-child "^3.1.0"
      jackspeak "^3.1.2"
      minimatch "^9.0.4"
      minipass "^7.1.2"
      package-json-from-dist "^1.0.0"
      path-scurry "^1.11.1"
  
  glob@^7.1.3:
    version "7.2.3"
    resolved "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
    integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
    dependencies:
      fs.realpath "^1.0.0"
      inflight "^1.0.4"
      inherits "2"
      minimatch "^3.1.1"
      once "^1.3.0"
      path-is-absolute "^1.0.0"
  
  glob@10.3.10:
    version "10.3.10"
    resolved "https://registry.npmjs.org/glob/-/glob-10.3.10.tgz"
    integrity sha512-fa46+tv1Ak0UPK1TOy/pZrIybNNt4HCv7SDzwyfiOZkvZLEbjsZkJBPtDHVshZjbecAoAGSC20MjLDG/qr679g==
    dependencies:
      foreground-child "^3.1.0"
      jackspeak "^2.3.5"
      minimatch "^9.0.1"
      minipass "^5.0.0 || ^6.0.2 || ^7.0.0"
      path-scurry "^1.10.1"
  
  globals@^13.19.0:
    version "13.24.0"
    resolved "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz"
    integrity sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==
    dependencies:
      type-fest "^0.20.2"
  
  globalthis@^1.0.3:
    version "1.0.4"
    resolved "https://registry.npmjs.org/globalthis/-/globalthis-1.0.4.tgz"
    integrity sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==
    dependencies:
      define-properties "^1.2.1"
      gopd "^1.0.1"
  
  globby@^11.1.0:
    version "11.1.0"
    resolved "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz"
    integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
    dependencies:
      array-union "^2.1.0"
      dir-glob "^3.0.1"
      fast-glob "^3.2.9"
      ignore "^5.2.0"
      merge2 "^1.4.1"
      slash "^3.0.0"
  
  gopd@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz"
    integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
    dependencies:
      get-intrinsic "^1.1.3"
  
  graceful-fs@^4.2.11, graceful-fs@^4.2.4:
    version "4.2.11"
    resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
    integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==
  
  graphemer@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz"
    integrity sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==
  
  has-bigints@^1.0.1, has-bigints@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/has-bigints/-/has-bigints-1.0.2.tgz"
    integrity sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==
  
  has-flag@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
    integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==
  
  has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
    integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
    dependencies:
      es-define-property "^1.0.0"
  
  has-proto@^1.0.1, has-proto@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/has-proto/-/has-proto-1.0.3.tgz"
    integrity sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==
  
  has-symbols@^1.0.2, has-symbols@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz"
    integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==
  
  has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
    integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
    dependencies:
      has-symbols "^1.0.3"
  
  hasown@^2.0.0, hasown@^2.0.1, hasown@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
    integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
    dependencies:
      function-bind "^1.1.2"
  
  husky@^9.0.11:
    version "9.0.11"
    resolved "https://registry.npmjs.org/husky/-/husky-9.0.11.tgz"
    integrity sha512-AB6lFlbwwyIqMdHYhwPe+kjOC3Oc5P3nThEoW/AaO2BX3vJDjWPFxYLxokUZOo6RNX20He3AaT8sESs9NJcmEw==
  
  ignore@^5.2.0, ignore@^5.3.1:
    version "5.3.1"
    resolved "https://registry.npmjs.org/ignore/-/ignore-5.3.1.tgz"
    integrity sha512-5Fytz/IraMjqpwfd34ke28PTVMjZjJG2MPn5t7OE4eUCUNf8BAa7b5WUS9/Qvr6mwOQS7Mk6vdsMno5he+T8Xw==
  
  import-fresh@^3.2.1:
    version "3.3.0"
    resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
    integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
    dependencies:
      parent-module "^1.0.0"
      resolve-from "^4.0.0"
  
  imurmurhash@^0.1.4:
    version "0.1.4"
    resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
    integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==
  
  inflight@^1.0.4:
    version "1.0.6"
    resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
    integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
    dependencies:
      once "^1.3.0"
      wrappy "1"
  
  inherits@2:
    version "2.0.4"
    resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
    integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==
  
  internal-slot@^1.0.4, internal-slot@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.7.tgz"
    integrity sha512-NGnrKwXzSms2qUUih/ILZ5JBqNTSa1+ZmP6flaIp6KmSElgE9qdndzS3cqjrDovwFdmwsGsLdeFgB6suw+1e9g==
    dependencies:
      es-errors "^1.3.0"
      hasown "^2.0.0"
      side-channel "^1.0.4"
  
  invariant@^2.2.4:
    version "2.2.4"
    resolved "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz"
    integrity sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==
    dependencies:
      loose-envify "^1.0.0"
  
  is-arguments@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz"
    integrity sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-array-buffer@^3.0.2, is-array-buffer@^3.0.4:
    version "3.0.4"
    resolved "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.4.tgz"
    integrity sha512-wcjaerHw0ydZwfhiKbXJWLDY8A7yV7KhjQOpb83hGgGfId/aQa4TOvwyzn2PuswW2gPCYEL/nEAiSVpdOj1lXw==
    dependencies:
      call-bind "^1.0.2"
      get-intrinsic "^1.2.1"
  
  is-async-function@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/is-async-function/-/is-async-function-2.0.0.tgz"
    integrity sha512-Y1JXKrfykRJGdlDwdKlLpLyMIiWqWvuSd17TvZk68PLAOGOoF4Xyav1z0Xhoi+gCYjZVeC5SI+hYFOfvXmGRCA==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-bigint@^1.0.1:
    version "1.0.4"
    resolved "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.4.tgz"
    integrity sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==
    dependencies:
      has-bigints "^1.0.1"
  
  is-binary-path@~2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
    integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
    dependencies:
      binary-extensions "^2.0.0"
  
  is-boolean-object@^1.1.0:
    version "1.1.2"
    resolved "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
    integrity sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
    version "1.2.7"
    resolved "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz"
    integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==
  
  is-core-module@^2.11.0, is-core-module@^2.13.0, is-core-module@^2.13.1:
    version "2.14.0"
    resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.14.0.tgz"
    integrity sha512-a5dFJih5ZLYlRtDc0dZWP7RiKr6xIKzmn/oAYCDvdLThadVgyJwlaoQPmRtMSpz+rk0OGAgIu+TcM9HUF0fk1A==
    dependencies:
      hasown "^2.0.2"
  
  is-data-view@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/is-data-view/-/is-data-view-1.0.1.tgz"
    integrity sha512-AHkaJrsUVW6wq6JS8y3JnM/GJF/9cf+k20+iDzlSaJrinEo5+7vRiteOSwBhHRiAyQATN1AmY4hwzxJKPmYf+w==
    dependencies:
      is-typed-array "^1.1.13"
  
  is-date-object@^1.0.1, is-date-object@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz"
    integrity sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-extglob@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
    integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==
  
  is-finalizationregistry@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/is-finalizationregistry/-/is-finalizationregistry-1.0.2.tgz"
    integrity sha512-0by5vtUJs8iFQb5TYUHHPudOR+qXYIMKtiUzvLIZITZUjknFmziyBJuLhVRc+Ds0dREFlskDNJKYIdIzu/9pfw==
    dependencies:
      call-bind "^1.0.2"
  
  is-fullwidth-code-point@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
    integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==
  
  is-generator-function@^1.0.10:
    version "1.0.10"
    resolved "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.0.10.tgz"
    integrity sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
    version "4.0.3"
    resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
    integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
    dependencies:
      is-extglob "^2.1.1"
  
  is-map@^2.0.2, is-map@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmjs.org/is-map/-/is-map-2.0.3.tgz"
    integrity sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==
  
  is-negative-zero@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.3.tgz"
    integrity sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==
  
  is-number-object@^1.0.4:
    version "1.0.7"
    resolved "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.7.tgz"
    integrity sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-number@^7.0.0:
    version "7.0.0"
    resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
    integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==
  
  is-path-inside@^3.0.3:
    version "3.0.3"
    resolved "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz"
    integrity sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==
  
  is-regex@^1.1.4:
    version "1.1.4"
    resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz"
    integrity sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-set@^2.0.2, is-set@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmjs.org/is-set/-/is-set-2.0.3.tgz"
    integrity sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==
  
  is-shared-array-buffer@^1.0.2, is-shared-array-buffer@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.3.tgz"
    integrity sha512-nA2hv5XIhLR3uVzDDfCIknerhx8XUKnstuOERPNNIinXG7v9u+ohXF67vxm4TPTEPU6lm61ZkwP3c9PCB97rhg==
    dependencies:
      call-bind "^1.0.7"
  
  is-string@^1.0.5, is-string@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmjs.org/is-string/-/is-string-1.0.7.tgz"
    integrity sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-symbol@^1.0.2, is-symbol@^1.0.3:
    version "1.0.4"
    resolved "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.4.tgz"
    integrity sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==
    dependencies:
      has-symbols "^1.0.2"
  
  is-typed-array@^1.1.13:
    version "1.1.13"
    resolved "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.13.tgz"
    integrity sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw==
    dependencies:
      which-typed-array "^1.1.14"
  
  is-weakmap@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.2.tgz"
    integrity sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==
  
  is-weakref@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/is-weakref/-/is-weakref-1.0.2.tgz"
    integrity sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==
    dependencies:
      call-bind "^1.0.2"
  
  is-weakset@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.3.tgz"
    integrity sha512-LvIm3/KWzS9oRFHugab7d+M/GcBXuXX5xZkzPmN+NxihdQlZUQ4dWuSV1xR/sq6upL1TJEDrfBgRepHFdBtSNQ==
    dependencies:
      call-bind "^1.0.7"
      get-intrinsic "^1.2.4"
  
  isarray@^2.0.5:
    version "2.0.5"
    resolved "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz"
    integrity sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==
  
  isexe@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
    integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==
  
  iterator.prototype@^1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/iterator.prototype/-/iterator.prototype-1.1.2.tgz"
    integrity sha512-DR33HMMr8EzwuRL8Y9D3u2BMj8+RqSE850jfGu59kS7tbmPLzGkZmVSfyCFSDxuZiEY6Rzt3T2NA/qU+NwVj1w==
    dependencies:
      define-properties "^1.2.1"
      get-intrinsic "^1.2.1"
      has-symbols "^1.0.3"
      reflect.getprototypeof "^1.0.4"
      set-function-name "^2.0.1"
  
  jackspeak@^2.3.5:
    version "2.3.6"
    resolved "https://registry.npmjs.org/jackspeak/-/jackspeak-2.3.6.tgz"
    integrity sha512-N3yCS/NegsOBokc8GAdM8UcmfsKiSS8cipheD/nivzr700H+nsMOxJjQnvwOcRYVuFkdH0wGUvW2WbXGmrZGbQ==
    dependencies:
      "@isaacs/cliui" "^8.0.2"
    optionalDependencies:
      "@pkgjs/parseargs" "^0.11.0"
  
  jackspeak@^3.1.2:
    version "3.4.0"
    resolved "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.0.tgz"
    integrity sha512-JVYhQnN59LVPFCEcVa2C3CrEKYacvjRfqIQl+h8oi91aLYQVWRYbxjPcv1bUiUy/kLmQaANrYfNMCO3kuEDHfw==
    dependencies:
      "@isaacs/cliui" "^8.0.2"
    optionalDependencies:
      "@pkgjs/parseargs" "^0.11.0"
  
  jiti@^1.21.0:
    version "1.21.6"
    resolved "https://registry.npmjs.org/jiti/-/jiti-1.21.6.tgz"
    integrity sha512-2yTgeWTWzMWkHu6Jp9NKgePDaYHbntiwvYuuJLbbN9vl7DC9DvXKOB2BC3ZZ92D3cvV/aflH0osDfwpHepQ53w==
  
  "js-tokens@^3.0.0 || ^4.0.0":
    version "4.0.0"
    resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
    integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==
  
  js-yaml@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
    integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
    dependencies:
      argparse "^2.0.1"
  
  json-buffer@3.0.1:
    version "3.0.1"
    resolved "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz"
    integrity sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==
  
  json-schema-traverse@^0.4.1:
    version "0.4.1"
    resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
    integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==
  
  json-stable-stringify-without-jsonify@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
    integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==
  
  json5@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz"
    integrity sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==
    dependencies:
      minimist "^1.2.0"
  
  "jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.5:
    version "3.3.5"
    resolved "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz"
    integrity sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==
    dependencies:
      array-includes "^3.1.6"
      array.prototype.flat "^1.3.1"
      object.assign "^4.1.4"
      object.values "^1.1.6"
  
  keyv@^4.5.3:
    version "4.5.4"
    resolved "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz"
    integrity sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==
    dependencies:
      json-buffer "3.0.1"
  
  language-subtag-registry@^0.3.20:
    version "0.3.23"
    resolved "https://registry.npmjs.org/language-subtag-registry/-/language-subtag-registry-0.3.23.tgz"
    integrity sha512-0K65Lea881pHotoGEa5gDlMxt3pctLi2RplBb7Ezh4rRdLEOtgi7n4EwK9lamnUCkKBqaeKRVebTq6BAxSkpXQ==
  
  language-tags@^1.0.9:
    version "1.0.9"
    resolved "https://registry.npmjs.org/language-tags/-/language-tags-1.0.9.tgz"
    integrity sha512-MbjN408fEndfiQXbFQ1vnd+1NoLDsnQW41410oQBXiyXDMYH5z505juWa4KUE1LqxRC7DgOgZDbKLxHIwm27hA==
    dependencies:
      language-subtag-registry "^0.3.20"
  
  levn@^0.4.1:
    version "0.4.1"
    resolved "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
    integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
    dependencies:
      prelude-ls "^1.2.1"
      type-check "~0.4.0"
  
  lilconfig@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-2.1.0.tgz"
    integrity sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==
  
  lilconfig@^3.0.0:
    version "3.1.2"
    resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.2.tgz"
    integrity sha512-eop+wDAvpItUys0FWkHIKeC9ybYrTGbU41U5K7+bttZZeohvnY7M9dZ5kB21GNWiFT2q1OoPTvncPCgSOVO5ow==
  
  lines-and-columns@^1.1.6:
    version "1.2.4"
    resolved "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
    integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==
  
  load-script@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/load-script/-/load-script-1.0.0.tgz"
    integrity sha512-kPEjMFtZvwL9TaZo0uZ2ml+Ye9HUMmPwbYRJ324qF9tqMejwykJ5ggTyvzmrbBeapCAbk98BSbTeovHEEP1uCA==
  
  locate-path@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
    integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
    dependencies:
      p-locate "^5.0.0"
  
  lodash.merge@^4.6.2:
    version "4.6.2"
    resolved "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
    integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==
  
  loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
    integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
    dependencies:
      js-tokens "^3.0.0 || ^4.0.0"
  
  lru-cache@^10.2.0:
    version "10.3.0"
    resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-10.3.0.tgz"
    integrity sha512-CQl19J/g+Hbjbv4Y3mFNNXFEL/5t/KCg8POCuUqd4rMKjGG+j1ybER83hxV58zL+dFI1PTkt3GNFSHRt+d8qEQ==
  
  lucide-react@^0.399.0:
    version "0.399.0"
    resolved "https://registry.npmjs.org/lucide-react/-/lucide-react-0.399.0.tgz"
    integrity sha512-UyTNa3djBISdzL2UktgCrESXexQXaDQWi/WsDkbw6fBFfHlapajR58WoR+gxQ4laxfEyiHmoFrEIM3V+5XOVQg==
  
  memoize-one@^5.1.1:
    version "5.2.1"
    resolved "https://registry.npmjs.org/memoize-one/-/memoize-one-5.2.1.tgz"
    integrity sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q==
  
  merge2@^1.3.0, merge2@^1.4.1:
    version "1.4.1"
    resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
    integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==
  
  micromatch@^4.0.4, micromatch@^4.0.5:
    version "4.0.7"
    resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.7.tgz"
    integrity sha512-LPP/3KorzCwBxfeUuZmaR6bG2kdeHSbe0P2tY3FLRU4vYrjYz5hI4QZwV0njUx3jeuKe67YukQ1LSPZBKDqO/Q==
    dependencies:
      braces "^3.0.3"
      picomatch "^2.3.1"
  
  minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
    version "3.1.2"
    resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
    integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
    dependencies:
      brace-expansion "^1.1.7"
  
  minimatch@^9.0.1:
    version "9.0.5"
    resolved "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz"
    integrity sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==
    dependencies:
      brace-expansion "^2.0.1"
  
  minimatch@^9.0.4:
    version "9.0.5"
    resolved "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz"
    integrity sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==
    dependencies:
      brace-expansion "^2.0.1"
  
  minimatch@9.0.3:
    version "9.0.3"
    resolved "https://registry.npmjs.org/minimatch/-/minimatch-9.0.3.tgz"
    integrity sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==
    dependencies:
      brace-expansion "^2.0.1"
  
  minimist@^1.2.0, minimist@^1.2.6:
    version "1.2.8"
    resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
    integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==
  
  "minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.1.2:
    version "7.1.2"
    resolved "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
    integrity sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==
  
  ms@^2.1.1:
    version "2.1.3"
    resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
    integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==
  
  ms@2.1.2:
    version "2.1.2"
    resolved "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
    integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==
  
  mz@^2.7.0:
    version "2.7.0"
    resolved "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz"
    integrity sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
    dependencies:
      any-promise "^1.0.0"
      object-assign "^4.0.1"
      thenify-all "^1.0.0"
  
  nanoid@^3.3.6, nanoid@^3.3.7:
    version "3.3.7"
    resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.7.tgz"
    integrity sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==
  
  natural-compare@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
    integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==
  
  next@14.2.4:
    version "14.2.4"
    resolved "https://registry.npmjs.org/next/-/next-14.2.4.tgz"
    integrity sha512-R8/V7vugY+822rsQGQCjoLhMuC9oFj9SOi4Cl4b2wjDrseD0LRZ10W7R6Czo4w9ZznVSshKjuIomsRjvm9EKJQ==
    dependencies:
      "@next/env" "14.2.4"
      "@swc/helpers" "0.5.5"
      busboy "1.6.0"
      caniuse-lite "^1.0.30001579"
      graceful-fs "^4.2.11"
      postcss "8.4.31"
      styled-jsx "5.1.1"
    optionalDependencies:
      "@next/swc-darwin-arm64" "14.2.4"
      "@next/swc-darwin-x64" "14.2.4"
      "@next/swc-linux-arm64-gnu" "14.2.4"
      "@next/swc-linux-arm64-musl" "14.2.4"
      "@next/swc-linux-x64-gnu" "14.2.4"
      "@next/swc-linux-x64-musl" "14.2.4"
      "@next/swc-win32-arm64-msvc" "14.2.4"
      "@next/swc-win32-ia32-msvc" "14.2.4"
      "@next/swc-win32-x64-msvc" "14.2.4"
  
  normalize-path@^3.0.0, normalize-path@~3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
    integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==
  
  object-assign@^4.0.1, object-assign@^4.1.1:
    version "4.1.1"
    resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
    integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==
  
  object-hash@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz"
    integrity sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==
  
  object-inspect@^1.13.1:
    version "1.13.2"
    resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.2.tgz"
    integrity sha512-IRZSRuzJiynemAXPYtPe5BoI/RESNYR7TYm50MC5Mqbd3Jmw5y790sErYw3V6SryFJD64b74qQQs9wn5Bg/k3g==
  
  object-is@^1.1.5:
    version "1.1.6"
    resolved "https://registry.npmjs.org/object-is/-/object-is-1.1.6.tgz"
    integrity sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
  
  object-keys@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
    integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==
  
  object.assign@^4.1.4, object.assign@^4.1.5:
    version "4.1.5"
    resolved "https://registry.npmjs.org/object.assign/-/object.assign-4.1.5.tgz"
    integrity sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==
    dependencies:
      call-bind "^1.0.5"
      define-properties "^1.2.1"
      has-symbols "^1.0.3"
      object-keys "^1.1.1"
  
  object.entries@^1.1.8:
    version "1.1.8"
    resolved "https://registry.npmjs.org/object.entries/-/object.entries-1.1.8.tgz"
    integrity sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-object-atoms "^1.0.0"
  
  object.fromentries@^2.0.7, object.fromentries@^2.0.8:
    version "2.0.8"
    resolved "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.8.tgz"
    integrity sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.2"
      es-object-atoms "^1.0.0"
  
  object.groupby@^1.0.1:
    version "1.0.3"
    resolved "https://registry.npmjs.org/object.groupby/-/object.groupby-1.0.3.tgz"
    integrity sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.2"
  
  object.hasown@^1.1.4:
    version "1.1.4"
    resolved "https://registry.npmjs.org/object.hasown/-/object.hasown-1.1.4.tgz"
    integrity sha512-FZ9LZt9/RHzGySlBARE3VF+gE26TxR38SdmqOqliuTnl9wrKulaQs+4dee1V+Io8VfxqzAfHu6YuRgUy8OHoTg==
    dependencies:
      define-properties "^1.2.1"
      es-abstract "^1.23.2"
      es-object-atoms "^1.0.0"
  
  object.values@^1.1.6, object.values@^1.1.7, object.values@^1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/object.values/-/object.values-1.2.0.tgz"
    integrity sha512-yBYjY9QX2hnRmZHAjG/f13MzmBzxzYgQhFrke06TTyKY5zSTEqkOeukBzIdVA3j3ulu8Qa3MbVFShV7T2RmGtQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-object-atoms "^1.0.0"
  
  once@^1.3.0:
    version "1.4.0"
    resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
    integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
    dependencies:
      wrappy "1"
  
  optionator@^0.9.3:
    version "0.9.4"
    resolved "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz"
    integrity sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==
    dependencies:
      deep-is "^0.1.3"
      fast-levenshtein "^2.0.6"
      levn "^0.4.1"
      prelude-ls "^1.2.1"
      type-check "^0.4.0"
      word-wrap "^1.2.5"
  
  p-limit@^3.0.2:
    version "3.1.0"
    resolved "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
    integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
    dependencies:
      yocto-queue "^0.1.0"
  
  p-locate@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
    integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
    dependencies:
      p-limit "^3.0.2"
  
  package-json-from-dist@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.0.tgz"
    integrity sha512-dATvCeZN/8wQsGywez1mzHtTlP22H8OEfPrVMLNr4/eGa+ijtLn/6M5f0dY8UKNrC2O9UCU6SSoG3qRKnt7STw==
  
  parent-module@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
    integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
    dependencies:
      callsites "^3.0.0"
  
  path-exists@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
    integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==
  
  path-is-absolute@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
    integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==
  
  path-key@^3.1.0:
    version "3.1.1"
    resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
    integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==
  
  path-parse@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
    integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==
  
  path-scurry@^1.10.1, path-scurry@^1.11.1:
    version "1.11.1"
    resolved "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz"
    integrity sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==
    dependencies:
      lru-cache "^10.2.0"
      minipass "^5.0.0 || ^6.0.2 || ^7.0.0"
  
  path-type@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
    integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==
  
  picocolors@^1.0.0, picocolors@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.0.1.tgz"
    integrity sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==
  
  picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
    version "2.3.1"
    resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
    integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==
  
  pify@^2.3.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
    integrity sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==
  
  pirates@^4.0.1:
    version "4.0.6"
    resolved "https://registry.npmjs.org/pirates/-/pirates-4.0.6.tgz"
    integrity sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==
  
  possible-typed-array-names@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/possible-typed-array-names/-/possible-typed-array-names-1.0.0.tgz"
    integrity sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==
  
  postcss-import@^15.1.0:
    version "15.1.0"
    resolved "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz"
    integrity sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==
    dependencies:
      postcss-value-parser "^4.0.0"
      read-cache "^1.0.0"
      resolve "^1.1.7"
  
  postcss-js@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz"
    integrity sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==
    dependencies:
      camelcase-css "^2.0.1"
  
  postcss-load-config@^4.0.1:
    version "4.0.2"
    resolved "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz"
    integrity sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==
    dependencies:
      lilconfig "^3.0.0"
      yaml "^2.3.4"
  
  postcss-nested@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.0.1.tgz"
    integrity sha512-mEp4xPMi5bSWiMbsgoPfcP74lsWLHkQbZc3sY+jWYd65CUwXrUaTp0fmNpa01ZcETKlIgUdFN/MpS2xZtqL9dQ==
    dependencies:
      postcss-selector-parser "^6.0.11"
  
  postcss-selector-parser@^6.0.11:
    version "6.1.0"
    resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.0.tgz"
    integrity sha512-UMz42UD0UY0EApS0ZL9o1XnLhSTtvvvLe5Dc2H2O56fvRZi+KulDyf5ctDhhtYJBGKStV2FL1fy6253cmLgqVQ==
    dependencies:
      cssesc "^3.0.0"
      util-deprecate "^1.0.2"
  
  postcss-value-parser@^4.0.0:
    version "4.2.0"
    resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
    integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==
  
  postcss@^8, postcss@^8.4.23:
    version "8.4.39"
    resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.39.tgz"
    integrity sha512-0vzE+lAiG7hZl1/9I8yzKLx3aR9Xbof3fBHKunvMfOCYAtMhrsnccJY2iTURb9EZd5+pLuiNV9/c/GZJOHsgIw==
    dependencies:
      nanoid "^3.3.7"
      picocolors "^1.0.1"
      source-map-js "^1.2.0"
  
  postcss@8.4.31:
    version "8.4.31"
    resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.31.tgz"
    integrity sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==
    dependencies:
      nanoid "^3.3.6"
      picocolors "^1.0.0"
      source-map-js "^1.0.2"
  
  prelude-ls@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
    integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==
  
  prettier-linter-helpers@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
    integrity sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==
    dependencies:
      fast-diff "^1.1.2"
  
  prop-types@^15.5.8, prop-types@^15.7.2, prop-types@^15.8.1:
    version "15.8.1"
    resolved "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
    integrity sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
    dependencies:
      loose-envify "^1.4.0"
      object-assign "^4.1.1"
      react-is "^16.13.1"
  
  punycode@^2.1.0:
    version "2.3.1"
    resolved "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
    integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==
  
  queue-microtask@^1.2.2:
    version "1.2.3"
    resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
    integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==
  
  react-dom@^18:
    version "18.3.1"
    resolved "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz"
    integrity sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==
    dependencies:
      loose-envify "^1.1.0"
      scheduler "^0.23.2"
  
  react-easy-swipe@^0.0.21:
    version "0.0.21"
    resolved "https://registry.npmjs.org/react-easy-swipe/-/react-easy-swipe-0.0.21.tgz"
    integrity sha512-OeR2jAxdoqUMHIn/nS9fgreI5hSpgGoL5ezdal4+oO7YSSgJR8ga+PkYGJrSrJ9MKlPcQjMQXnketrD7WNmNsg==
    dependencies:
      prop-types "^15.5.8"
  
  react-fast-compare@^3.0.1:
    version "3.2.2"
    resolved "https://registry.npmjs.org/react-fast-compare/-/react-fast-compare-3.2.2.tgz"
    integrity sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ==
  
  react-hook-form@^7.52.0:
    version "7.52.0"
    resolved "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.52.0.tgz"
    integrity sha512-mJX506Xc6mirzLsmXUJyqlAI3Kj9Ph2RhplYhUVffeOQSnubK2uVqBFOBJmvKikvbFV91pxVXmDiR+QMF19x6A==
  
  react-icons@^5.2.1:
    version "5.2.1"
    resolved "https://registry.npmjs.org/react-icons/-/react-icons-5.2.1.tgz"
    integrity sha512-zdbW5GstTzXaVKvGSyTaBalt7HSfuK5ovrzlpyiWHAFXndXTdd/1hdDHI4xBM1Mn7YriT6aqESucFl9kEXzrdw==
  
  react-is@^16.13.1:
    version "16.13.1"
    resolved "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
    integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==
  
  react-player@^2.16.0:
    version "2.16.0"
    resolved "https://registry.npmjs.org/react-player/-/react-player-2.16.0.tgz"
    integrity sha512-mAIPHfioD7yxO0GNYVFD1303QFtI3lyyQZLY229UEAp/a10cSW+hPcakg0Keq8uWJxT2OiT/4Gt+Lc9bD6bJmQ==
    dependencies:
      deepmerge "^4.0.0"
      load-script "^1.0.0"
      memoize-one "^5.1.1"
      prop-types "^15.7.2"
      react-fast-compare "^3.0.1"
  
  react-remove-scroll-bar@^2.3.4:
    version "2.3.6"
    resolved "https://registry.npmjs.org/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.6.tgz"
    integrity sha512-DtSYaao4mBmX+HDo5YWYdBWQwYIQQshUV/dVxFxK+KM26Wjwp1gZ6rv6OC3oujI6Bfu6Xyg3TwK533AQutsn/g==
    dependencies:
      react-style-singleton "^2.2.1"
      tslib "^2.0.0"
  
  react-remove-scroll@2.5.7:
    version "2.5.7"
    resolved "https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.5.7.tgz"
    integrity sha512-FnrTWO4L7/Bhhf3CYBNArEG/yROV0tKmTv7/3h9QCFvH6sndeFf1wPqOcbFVu5VAulS5dV1wGT3GZZ/1GawqiA==
    dependencies:
      react-remove-scroll-bar "^2.3.4"
      react-style-singleton "^2.2.1"
      tslib "^2.1.0"
      use-callback-ref "^1.3.0"
      use-sidecar "^1.1.2"
  
  react-responsive-carousel@^3.2.23:
    version "3.2.23"
    resolved "https://registry.npmjs.org/react-responsive-carousel/-/react-responsive-carousel-3.2.23.tgz"
    integrity sha512-pqJLsBaKHWJhw/ItODgbVoziR2z4lpcJg+YwmRlSk4rKH32VE633mAtZZ9kDXjy4wFO+pgUZmDKPsPe1fPmHCg==
    dependencies:
      classnames "^2.2.5"
      prop-types "^15.5.8"
      react-easy-swipe "^0.0.21"
  
  react-style-singleton@^2.2.1:
    version "2.2.1"
    resolved "https://registry.npmjs.org/react-style-singleton/-/react-style-singleton-2.2.1.tgz"
    integrity sha512-ZWj0fHEMyWkHzKYUr2Bs/4zU6XLmq9HsgBURm7g5pAVfyn49DgUiNgY2d4lXRlYSiCif9YBGpQleewkcqddc7g==
    dependencies:
      get-nonce "^1.0.0"
      invariant "^2.2.4"
      tslib "^2.0.0"
  
  react@^18:
    version "18.3.1"
    resolved "https://registry.npmjs.org/react/-/react-18.3.1.tgz"
    integrity sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==
    dependencies:
      loose-envify "^1.1.0"
  
  read-cache@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz"
    integrity sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==
    dependencies:
      pify "^2.3.0"
  
  readdirp@~3.6.0:
    version "3.6.0"
    resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
    integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
    dependencies:
      picomatch "^2.2.1"
  
  reflect.getprototypeof@^1.0.4:
    version "1.0.6"
    resolved "https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.6.tgz"
    integrity sha512-fmfw4XgoDke3kdI6h4xcUz1dG8uaiv5q9gcEwLS4Pnth2kxT+GZ7YehS1JTMGBQmtV7Y4GFGbs2re2NqhdozUg==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.1"
      es-errors "^1.3.0"
      get-intrinsic "^1.2.4"
      globalthis "^1.0.3"
      which-builtin-type "^1.1.3"
  
  regenerator-runtime@^0.14.0:
    version "0.14.1"
    resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"
    integrity sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==
  
  regexp.prototype.flags@^1.5.1, regexp.prototype.flags@^1.5.2:
    version "1.5.2"
    resolved "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.2.tgz"
    integrity sha512-NcDiDkTLuPR+++OCKB0nWafEmhg/Da8aUPLPMQbK+bxKKCm1/S5he+AqYa4PlMCVBalb4/yxIRub6qkEx5yJbw==
    dependencies:
      call-bind "^1.0.6"
      define-properties "^1.2.1"
      es-errors "^1.3.0"
      set-function-name "^2.0.1"
  
  resolve-from@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
    integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==
  
  resolve-pkg-maps@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/resolve-pkg-maps/-/resolve-pkg-maps-1.0.0.tgz"
    integrity sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==
  
  resolve@^1.1.7, resolve@^1.22.2, resolve@^1.22.4:
    version "1.22.8"
    resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.8.tgz"
    integrity sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==
    dependencies:
      is-core-module "^2.13.0"
      path-parse "^1.0.7"
      supports-preserve-symlinks-flag "^1.0.0"
  
  resolve@^2.0.0-next.5:
    version "2.0.0-next.5"
    resolved "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.5.tgz"
    integrity sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==
    dependencies:
      is-core-module "^2.13.0"
      path-parse "^1.0.7"
      supports-preserve-symlinks-flag "^1.0.0"
  
  reusify@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
    integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==
  
  rimraf@^3.0.2:
    version "3.0.2"
    resolved "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
    integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
    dependencies:
      glob "^7.1.3"
  
  run-parallel@^1.1.9:
    version "1.2.0"
    resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
    integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
    dependencies:
      queue-microtask "^1.2.2"
  
  safe-array-concat@^1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.2.tgz"
    integrity sha512-vj6RsCsWBCf19jIeHEfkRMw8DPiBb+DMXklQ/1SGDHOMlHdPUkZXFQ2YdplS23zESTijAcurb1aSgJA3AgMu1Q==
    dependencies:
      call-bind "^1.0.7"
      get-intrinsic "^1.2.4"
      has-symbols "^1.0.3"
      isarray "^2.0.5"
  
  safe-regex-test@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.0.3.tgz"
    integrity sha512-CdASjNJPvRa7roO6Ra/gLYBTzYzzPyyBXxIMdGW3USQLyjWEls2RgW5UBTXaQVp+OrpeCK3bLem8smtmheoRuw==
    dependencies:
      call-bind "^1.0.6"
      es-errors "^1.3.0"
      is-regex "^1.1.4"
  
  scheduler@^0.23.2:
    version "0.23.2"
    resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz"
    integrity sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==
    dependencies:
      loose-envify "^1.1.0"
  
  semver@^6.3.1:
    version "6.3.1"
    resolved "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
    integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==
  
  semver@^7.5.4:
    version "7.6.2"
    resolved "https://registry.npmjs.org/semver/-/semver-7.6.2.tgz"
    integrity sha512-FNAIBWCx9qcRhoHcgcJ0gvU7SN1lYU2ZXuSfl04bSC5OpvDHFyJCjdNHomPXxjQlCBU67YW64PzY7/VIEH7F2w==
  
  semver@^7.6.0:
    version "7.6.2"
    resolved "https://registry.npmjs.org/semver/-/semver-7.6.2.tgz"
    integrity sha512-FNAIBWCx9qcRhoHcgcJ0gvU7SN1lYU2ZXuSfl04bSC5OpvDHFyJCjdNHomPXxjQlCBU67YW64PzY7/VIEH7F2w==
  
  set-function-length@^1.2.1:
    version "1.2.2"
    resolved "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz"
    integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
    dependencies:
      define-data-property "^1.1.4"
      es-errors "^1.3.0"
      function-bind "^1.1.2"
      get-intrinsic "^1.2.4"
      gopd "^1.0.1"
      has-property-descriptors "^1.0.2"
  
  set-function-name@^2.0.1, set-function-name@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz"
    integrity sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==
    dependencies:
      define-data-property "^1.1.4"
      es-errors "^1.3.0"
      functions-have-names "^1.2.3"
      has-property-descriptors "^1.0.2"
  
  shebang-command@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
    integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
    dependencies:
      shebang-regex "^3.0.0"
  
  shebang-regex@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
    integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==
  
  side-channel@^1.0.4, side-channel@^1.0.6:
    version "1.0.6"
    resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.0.6.tgz"
    integrity sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==
    dependencies:
      call-bind "^1.0.7"
      es-errors "^1.3.0"
      get-intrinsic "^1.2.4"
      object-inspect "^1.13.1"
  
  signal-exit@^4.0.1:
    version "4.1.0"
    resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz"
    integrity sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==
  
  slash@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
    integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==
  
  source-map-js@^1.0.2, source-map-js@^1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.0.tgz"
    integrity sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==
  
  stop-iteration-iterator@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/stop-iteration-iterator/-/stop-iteration-iterator-1.0.0.tgz"
    integrity sha512-iCGQj+0l0HOdZ2AEeBADlsRC+vsnDsZsbdSiH1yNSjcfKM7fdpCMfqAL/dwF5BLiw/XhRft/Wax6zQbhq2BcjQ==
    dependencies:
      internal-slot "^1.0.4"
  
  streamsearch@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz"
    integrity sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==
  
  "string-width-cjs@npm:string-width@^4.2.0":
    version "4.2.3"
    resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
    integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
    dependencies:
      emoji-regex "^8.0.0"
      is-fullwidth-code-point "^3.0.0"
      strip-ansi "^6.0.1"
  
  string-width@^4.1.0:
    version "4.2.3"
    resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
    integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
    dependencies:
      emoji-regex "^8.0.0"
      is-fullwidth-code-point "^3.0.0"
      strip-ansi "^6.0.1"
  
  string-width@^5.0.1, string-width@^5.1.2:
    version "5.1.2"
    resolved "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz"
    integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
    dependencies:
      eastasianwidth "^0.2.0"
      emoji-regex "^9.2.2"
      strip-ansi "^7.0.1"
  
  string.prototype.includes@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/string.prototype.includes/-/string.prototype.includes-2.0.0.tgz"
    integrity sha512-E34CkBgyeqNDcrbU76cDjL5JLcVrtSdYq0MEh/B10r17pRP4ciHLwTgnuLV8Ay6cgEMLkcBkFCKyFZ43YldYzg==
    dependencies:
      define-properties "^1.1.3"
      es-abstract "^1.17.5"
  
  string.prototype.matchall@^4.0.11:
    version "4.0.11"
    resolved "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.11.tgz"
    integrity sha512-NUdh0aDavY2og7IbBPenWqR9exH+E26Sv8e0/eTe1tltDGZL+GtBkDAnnyBtmekfK6/Dq3MkcGtzXFEd1LQrtg==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.2"
      es-errors "^1.3.0"
      es-object-atoms "^1.0.0"
      get-intrinsic "^1.2.4"
      gopd "^1.0.1"
      has-symbols "^1.0.3"
      internal-slot "^1.0.7"
      regexp.prototype.flags "^1.5.2"
      set-function-name "^2.0.2"
      side-channel "^1.0.6"
  
  string.prototype.trim@^1.2.9:
    version "1.2.9"
    resolved "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.9.tgz"
    integrity sha512-klHuCNxiMZ8MlsOihJhJEBJAiMVqU3Z2nEXWfWnIqjN0gEFS9J9+IxKozWWtQGcgoa1WUZzLjKPTr4ZHNFTFxw==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-abstract "^1.23.0"
      es-object-atoms "^1.0.0"
  
  string.prototype.trimend@^1.0.8:
    version "1.0.8"
    resolved "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.8.tgz"
    integrity sha512-p73uL5VCHCO2BZZ6krwwQE3kCzM7NKmis8S//xEC6fQonchbum4eP6kR4DLEjQFO3Wnj3Fuo8NM0kOSjVdHjZQ==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-object-atoms "^1.0.0"
  
  string.prototype.trimstart@^1.0.8:
    version "1.0.8"
    resolved "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz"
    integrity sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==
    dependencies:
      call-bind "^1.0.7"
      define-properties "^1.2.1"
      es-object-atoms "^1.0.0"
  
  "strip-ansi-cjs@npm:strip-ansi@^6.0.1":
    version "6.0.1"
    resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
    integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
    dependencies:
      ansi-regex "^5.0.1"
  
  strip-ansi@^6.0.0, strip-ansi@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
    integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
    dependencies:
      ansi-regex "^5.0.1"
  
  strip-ansi@^7.0.1:
    version "7.1.0"
    resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz"
    integrity sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==
    dependencies:
      ansi-regex "^6.0.1"
  
  strip-bom@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
    integrity sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==
  
  strip-json-comments@^3.1.1:
    version "3.1.1"
    resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
    integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==
  
  styled-jsx@5.1.1:
    version "5.1.1"
    resolved "https://registry.npmjs.org/styled-jsx/-/styled-jsx-5.1.1.tgz"
    integrity sha512-pW7uC1l4mBZ8ugbiZrcIsiIvVx1UmTfw7UkC3Um2tmfUq9Bhk8IiyEIPl6F8agHgjzku6j0xQEZbfA5uSgSaCw==
    dependencies:
      client-only "0.0.1"
  
  sucrase@^3.32.0:
    version "3.35.0"
    resolved "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz"
    integrity sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==
    dependencies:
      "@jridgewell/gen-mapping" "^0.3.2"
      commander "^4.0.0"
      glob "^10.3.10"
      lines-and-columns "^1.1.6"
      mz "^2.7.0"
      pirates "^4.0.1"
      ts-interface-checker "^0.1.9"
  
  supports-color@^7.1.0:
    version "7.2.0"
    resolved "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
    integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
    dependencies:
      has-flag "^4.0.0"
  
  supports-preserve-symlinks-flag@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
    integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==
  
  synckit@^0.8.6:
    version "0.8.8"
    resolved "https://registry.npmjs.org/synckit/-/synckit-0.8.8.tgz"
    integrity sha512-HwOKAP7Wc5aRGYdKH+dw0PRRpbO841v2DENBtjnR5HFWoiNByAl7vrx3p0G/rCyYXQsrxqtX48TImFtPcIHSpQ==
    dependencies:
      "@pkgr/core" "^0.1.0"
      tslib "^2.6.2"
  
  tailwind-merge@^2.3.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-2.3.0.tgz"
    integrity sha512-vkYrLpIP+lgR0tQCG6AP7zZXCTLc1Lnv/CCRT3BqJ9CZ3ui2++GPaGb1x/ILsINIMSYqqvrpqjUFsMNLlW99EA==
    dependencies:
      "@babel/runtime" "^7.24.1"
  
  tailwindcss-animate@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmjs.org/tailwindcss-animate/-/tailwindcss-animate-1.0.7.tgz"
    integrity sha512-bl6mpH3T7I3UFxuvDEXLxy/VuFxBk5bbzplh7tXI68mwMokNYd1t9qPBHlnyTwfa4JGC4zP516I1hYYtQ/vspA==
  
  tailwindcss@^3.4.4:
    version "3.4.4"
    resolved "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.4.tgz"
    integrity sha512-ZoyXOdJjISB7/BcLTR6SEsLgKtDStYyYZVLsUtWChO4Ps20CBad7lfJKVDiejocV4ME1hLmyY0WJE3hSDcmQ2A==
    dependencies:
      "@alloc/quick-lru" "^5.2.0"
      arg "^5.0.2"
      chokidar "^3.5.3"
      didyoumean "^1.2.2"
      dlv "^1.1.3"
      fast-glob "^3.3.0"
      glob-parent "^6.0.2"
      is-glob "^4.0.3"
      jiti "^1.21.0"
      lilconfig "^2.1.0"
      micromatch "^4.0.5"
      normalize-path "^3.0.0"
      object-hash "^3.0.0"
      picocolors "^1.0.0"
      postcss "^8.4.23"
      postcss-import "^15.1.0"
      postcss-js "^4.0.1"
      postcss-load-config "^4.0.1"
      postcss-nested "^6.0.1"
      postcss-selector-parser "^6.0.11"
      resolve "^1.22.2"
      sucrase "^3.32.0"
  
  tapable@^2.2.0:
    version "2.2.1"
    resolved "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz"
    integrity sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==
  
  text-table@^0.2.0:
    version "0.2.0"
    resolved "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
    integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==
  
  thenify-all@^1.0.0:
    version "1.6.0"
    resolved "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz"
    integrity sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==
    dependencies:
      thenify ">= 3.1.0 < 4"
  
  "thenify@>= 3.1.0 < 4":
    version "3.3.1"
    resolved "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz"
    integrity sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
    dependencies:
      any-promise "^1.0.0"
  
  to-regex-range@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
    integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
    dependencies:
      is-number "^7.0.0"
  
  ts-api-utils@^1.0.1, ts-api-utils@^1.3.0:
    version "1.3.0"
    resolved "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.3.0.tgz"
    integrity sha512-UQMIo7pb8WRomKR1/+MFVLTroIvDVtMX3K6OUir8ynLyzB8Jeriont2bTAtmNPa1ekAgN7YPDyf6V+ygrdU+eQ==
  
  ts-interface-checker@^0.1.9:
    version "0.1.13"
    resolved "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz"
    integrity sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==
  
  tsconfig-paths@^3.15.0:
    version "3.15.0"
    resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.15.0.tgz"
    integrity sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==
    dependencies:
      "@types/json5" "^0.0.29"
      json5 "^1.0.2"
      minimist "^1.2.6"
      strip-bom "^3.0.0"
  
  tslib@^2.0.0, tslib@^2.1.0, tslib@^2.4.0, tslib@^2.6.2:
    version "2.6.3"
    resolved "https://registry.npmjs.org/tslib/-/tslib-2.6.3.tgz"
    integrity sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ==
  
  type-check@^0.4.0, type-check@~0.4.0:
    version "0.4.0"
    resolved "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
    integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
    dependencies:
      prelude-ls "^1.2.1"
  
  type-fest@^0.20.2:
    version "0.20.2"
    resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
    integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==
  
  typed-array-buffer@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.2.tgz"
    integrity sha512-gEymJYKZtKXzzBzM4jqa9w6Q1Jjm7x2d+sh19AdsD4wqnMPDYyvwpsIc2Q/835kHuo3BEQ7CjelGhfTsoBb2MQ==
    dependencies:
      call-bind "^1.0.7"
      es-errors "^1.3.0"
      is-typed-array "^1.1.13"
  
  typed-array-byte-length@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.1.tgz"
    integrity sha512-3iMJ9q0ao7WE9tWcaYKIptkNBuOIcZCCT0d4MRvuuH88fEoEH62IuQe0OtraD3ebQEoTRk8XCBoknUNc1Y67pw==
    dependencies:
      call-bind "^1.0.7"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-proto "^1.0.3"
      is-typed-array "^1.1.13"
  
  typed-array-byte-offset@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.2.tgz"
    integrity sha512-Ous0vodHa56FviZucS2E63zkgtgrACj7omjwd/8lTEMEPFFyjfixMZ1ZXenpgCFBBt4EC1J2XsyVS2gkG0eTFA==
    dependencies:
      available-typed-arrays "^1.0.7"
      call-bind "^1.0.7"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-proto "^1.0.3"
      is-typed-array "^1.1.13"
  
  typed-array-length@^1.0.6:
    version "1.0.6"
    resolved "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.6.tgz"
    integrity sha512-/OxDN6OtAk5KBpGb28T+HZc2M+ADtvRxXrKKbUwtsLgdoxgX13hyy7ek6bFRl5+aBs2yZzB0c4CnQfAtVypW/g==
    dependencies:
      call-bind "^1.0.7"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-proto "^1.0.3"
      is-typed-array "^1.1.13"
      possible-typed-array-names "^1.0.0"
  
  typescript@5.1:
    version "5.1.6"
    resolved "https://registry.npmjs.org/typescript/-/typescript-5.1.6.tgz"
    integrity sha512-zaWCozRZ6DLEWAWFrVDz1H6FVXzUSfTy5FUMWsQlU8Ym5JP9eO4xkTIROFCQvhQf61z6O/G6ugw3SgAnvvm+HA==
  
  unbox-primitive@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.0.2.tgz"
    integrity sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==
    dependencies:
      call-bind "^1.0.2"
      has-bigints "^1.0.2"
      has-symbols "^1.0.3"
      which-boxed-primitive "^1.0.2"
  
  undici-types@~5.26.4:
    version "5.26.5"
    resolved "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz"
    integrity sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==
  
  uri-js@^4.2.2:
    version "4.4.1"
    resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
    integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
    dependencies:
      punycode "^2.1.0"
  
  use-callback-ref@^1.3.0:
    version "1.3.2"
    resolved "https://registry.npmjs.org/use-callback-ref/-/use-callback-ref-1.3.2.tgz"
    integrity sha512-elOQwe6Q8gqZgDA8mrh44qRTQqpIHDcZ3hXTLjBe1i4ph8XpNJnO+aQf3NaG+lriLopI4HMx9VjQLfPQ6vhnoA==
    dependencies:
      tslib "^2.0.0"
  
  use-sidecar@^1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/use-sidecar/-/use-sidecar-1.1.2.tgz"
    integrity sha512-epTbsLuzZ7lPClpz2TyryBfztm7m+28DlEv2ZCQ3MDr5ssiwyOwGH/e5F9CkfWjJ1t4clvI58yF822/GUkjjhw==
    dependencies:
      detect-node-es "^1.1.0"
      tslib "^2.0.0"
  
  util-deprecate@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
    integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==
  
  which-boxed-primitive@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
    integrity sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==
    dependencies:
      is-bigint "^1.0.1"
      is-boolean-object "^1.1.0"
      is-number-object "^1.0.4"
      is-string "^1.0.5"
      is-symbol "^1.0.3"
  
  which-builtin-type@^1.1.3:
    version "1.1.3"
    resolved "https://registry.npmjs.org/which-builtin-type/-/which-builtin-type-1.1.3.tgz"
    integrity sha512-YmjsSMDBYsM1CaFiayOVT06+KJeXf0o5M/CAd4o1lTadFAtacTUM49zoYxr/oroopFDfhvN6iEcBxUyc3gvKmw==
    dependencies:
      function.prototype.name "^1.1.5"
      has-tostringtag "^1.0.0"
      is-async-function "^2.0.0"
      is-date-object "^1.0.5"
      is-finalizationregistry "^1.0.2"
      is-generator-function "^1.0.10"
      is-regex "^1.1.4"
      is-weakref "^1.0.2"
      isarray "^2.0.5"
      which-boxed-primitive "^1.0.2"
      which-collection "^1.0.1"
      which-typed-array "^1.1.9"
  
  which-collection@^1.0.1:
    version "1.0.2"
    resolved "https://registry.npmjs.org/which-collection/-/which-collection-1.0.2.tgz"
    integrity sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==
    dependencies:
      is-map "^2.0.3"
      is-set "^2.0.3"
      is-weakmap "^2.0.2"
      is-weakset "^2.0.3"
  
  which-typed-array@^1.1.13, which-typed-array@^1.1.14, which-typed-array@^1.1.15, which-typed-array@^1.1.9:
    version "1.1.15"
    resolved "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.15.tgz"
    integrity sha512-oV0jmFtUky6CXfkqehVvBP/LSWJ2sy4vWMioiENyJLePrBO/yKyV9OyJySfAKosh+RYkIl5zJCNZ8/4JncrpdA==
    dependencies:
      available-typed-arrays "^1.0.7"
      call-bind "^1.0.7"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-tostringtag "^1.0.2"
  
  which@^2.0.1:
    version "2.0.2"
    resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
    integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
    dependencies:
      isexe "^2.0.0"
  
  word-wrap@^1.2.5:
    version "1.2.5"
    resolved "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz"
    integrity sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==
  
  "wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
    version "7.0.0"
    resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
    integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
    dependencies:
      ansi-styles "^4.0.0"
      string-width "^4.1.0"
      strip-ansi "^6.0.0"
  
  wrap-ansi@^8.1.0:
    version "8.1.0"
    resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
    integrity sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==
    dependencies:
      ansi-styles "^6.1.0"
      string-width "^5.0.1"
      strip-ansi "^7.0.1"
  
  wrappy@1:
    version "1.0.2"
    resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
    integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==
  
  yaml@^2.3.4:
    version "2.4.5"
    resolved "https://registry.npmjs.org/yaml/-/yaml-2.4.5.tgz"
    integrity sha512-aBx2bnqDzVOyNKfsysjA2ms5ZlnjSAW2eG3/L5G/CSujfjLJTJsEw1bGw8kCf04KodQWk1pxlGnZ56CRxiawmg==
  
  yocto-queue@^0.1.0:
    version "0.1.0"
    resolved "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
    integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==
  
  zod@^3.23.8:
    version "3.23.8"
    resolved "https://registry.npmjs.org/zod/-/zod-3.23.8.tgz"
    integrity sha512-XBx9AXhXktjUqnepgTiE5flcKIYWi/rme0Eaj+5Y0lftuGBq+jyRu/md4WnuxqgP1ubdpNCsYEYPxrzVHD8d6g==
